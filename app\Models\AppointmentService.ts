import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Appointment from './Appointment'
import Service from './Service'

export default class AppointmentService extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'service_id' })
  public serviceId: number | null

  @column({ columnName: 'appointment_id' })
  public appointmentId: number | null

  @column()
  public price: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Appointment,{
    foreignKey: 'appointmentId',
    localKey: 'id'
  })
  public appointment: BelongsTo<typeof Appointment>

  @belongsTo(() => Service,{
    foreignKey: 'serviceId',
    localKey: 'id'
  })
  public service: BelongsTo<typeof Service>
}
