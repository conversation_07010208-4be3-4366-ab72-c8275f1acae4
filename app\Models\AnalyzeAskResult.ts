import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany } from '@ioc:Adonis/Lucid/Orm'
import AnalyzeAskResultItem from './AnalyzeAskResultItem'
import { hasMany } from '@ioc:Adonis/Lucid/Orm'
import AnalyzeAsk from './AnalyzeAsk'
import Laboratory from './Laboratory'
import Laborantin from './Laborantin'
import Diagnostic from './Diagnostic'

export default class AnalyzeAskResult extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number

  @column({ columnName: 'laboratory_id' })
  public laboratoryId: number

  @column({ columnName: 'pro_id' })
  public proId: number

  @column({ columnName: 'is_positive' })
  public isPositive: boolean | null

  @column({columnName: 'is_view'})
  public isView: boolean | null

  @column({columnName: 'comment'})
  public comment: string | null

  @column({columnName: 'file_attachment'})
  public fileAttachment: string | null

  @column({columnName: 'token'})
  public token: string 

  @hasMany(() => AnalyzeAskResultItem,{
    foreignKey: 'analyzeAskResultId',
    localKey: 'id'
  })
  public items: HasMany<typeof AnalyzeAskResultItem>

  @belongsTo(()=>AnalyzeAsk,{
    foreignKey: 'analyzeAskId',
    localKey: 'id'
  })
  public analyze_ask: BelongsTo<typeof AnalyzeAsk>

  @belongsTo(()=>Laboratory,{
    foreignKey: 'laboratoryId',
    localKey: 'id'
  })
  public laboratory: BelongsTo<typeof Laboratory>

  @belongsTo(()=>Laborantin,{
    foreignKey: 'proId',
    localKey: 'id'
  })
  public pro: BelongsTo<typeof Laborantin>

  @belongsTo(()=>Diagnostic,{
    foreignKey: 'diagnosticId',
    localKey: 'id'
  })
  public diagnostic: BelongsTo<typeof Diagnostic>
}
