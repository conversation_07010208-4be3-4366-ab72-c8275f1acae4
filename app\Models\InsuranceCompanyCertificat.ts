import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class InsuranceCompanyCertificat extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'patient_insurance_company_id' })
  public patientInsuranceCompanyId: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'insurance_company_subscription_id' })
  public insuranceCompanySubscriptionId: number

  @column({ columnName: 'code_certificat' })
  public codeCertificat: string

  @column({ columnName: 'certificat_number' })
  public certificatNumber: string

  @column({ columnName: 'validity' })
  public validity: DateTime

  @column({ columnName: 'status' })
  public status: string
}
