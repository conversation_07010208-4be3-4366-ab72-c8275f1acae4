import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class OrderProposition extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public order_id: number

  @column()
  public pharmacy_id: number

  @column()
  public pro_id: number

  @column()
  public status: string

  @column()
  public simulation_id: number

  @column.dateTime({ autoCreate: true })
  public accepted_at: DateTime

  @column.dateTime({ autoCreate: true })
  public rejected_at: DateTime

  @column.dateTime({ autoCreate: true })
  public expired_at: DateTime

  @column.dateTime({ autoCreate: true })
  public payment_request_at: DateTime

  @column.dateTime({ autoCreate: true })
  public paid_at: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
