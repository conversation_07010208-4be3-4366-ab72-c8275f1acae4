import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Order from './Order'
import PrescriptionItem from './PrescriptionItem'
import AnalyzeAskItem from './AnalyzeAskItem'
import QuotationProposalItem from './QuotationProposalItem'

export default class OrderItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public orderId: number // Identifiant de la commande

  @column({ columnName: 'prescription_item_id' })
  public prescriptionItemId: number | null // Identifiant de l'élément de la prescription

  @column({ columnName: 'analyze_ask_item_id' })
  public analyzeAskItemId: number | null // Identifiant de l'élément de la demande d'analyse

  @column({ columnName: 'quotation_proposal_item_id' })
  public quotationProposalItemId: number | null // Identifiant de l'élément proposé par la demande de devis

  @column()
  public quantity: number // Nombre de produits proposés

  @column({ columnName: 'total_price' })
  public totalPrice: number | null // Prix du produit ou de l'analyse

  @column({ columnName: 'total_assured_price' })
  public totalAssuredPrice: number | null // Prix total assuré du produit ou de l'analyse

  @column({ columnName: 'paid_at' })
  public paidAt: string | null // Date à laquelle le produit ou l'analyse a été payé

  @column({ columnName: 'shipped_at' })
  public shippedAt: string | null // Date à laquelle le produit ou l'analyse a été expédié

  @column({ columnName: 'cancelled_at' })
  public cancelledAt: Date | null // Date à laquelle le produit ou l'analyse a été annulé

  @column()
  public status: 'paid' | 'partially_paid' | 'shipped' | 'cancelled' | 'pending'

  @belongsTo(() => Order, {
    foreignKey: 'orderId',
    localKey: 'id'
  })
  public order: BelongsTo<typeof Order>

  @belongsTo(() => PrescriptionItem, {
    foreignKey: 'prescriptionItemId',
    localKey: 'id'
  })
  public prescription_item: BelongsTo<typeof PrescriptionItem>

  @belongsTo(() => AnalyzeAskItem, {
    foreignKey: 'analyzeAskItemId',
    localKey: 'id'
  })
  public analyze_ask_item: BelongsTo<typeof AnalyzeAskItem>

  @belongsTo(() => QuotationProposalItem, {
    foreignKey: 'quotationProposalItemId',
    localKey: 'id'
  })
  public quotation_proposal_item: BelongsTo<typeof QuotationProposalItem>
}
