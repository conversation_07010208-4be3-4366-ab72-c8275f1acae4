import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator';
import { ApiResponse, UserStatus } from 'App/Controllers/Utils/models';
import Database from '@ioc:Adonis/Lucid/Database';
import HelperController from '../../helpers/HelperController';
import Laborantin from 'App/Models/Laborantin';
import { AuthContract } from '@ioc:Adonis/Addons/Auth';
import Laboratory from 'App/Models/Laboratory';
import Wallet from 'App/Models/Wallet';
import User from 'App/Models/User';
import QuotationRequest from 'App/Models/QuotationRequest';
import WalletHistory from 'App/Models/WalletHistory';
import AnalyzeAsk from 'App/Models/AnalyzeAsk';
import AnalyzeAskResult from 'App/Models/AnalyzeAskResult';
export default class LaboratoryController extends HelperController {

  public async getAuthPersonal(auth: AuthContract) {
    const authUser = await auth.authenticate();
    if (!authUser) {
      return null;
    }
    const laborantin = await Laborantin.query().where('user_id', authUser.id).preload('laboratory').first();
    return laborantin ? laborantin : null;
  }

  public async getLaboratoryIdByPersonal(auth: AuthContract) {
    const personal = await this.getAuthPersonal(auth);
    if (!personal) {
      return null;
    }
    return personal.laboratory_id;
  }

  public async getLaboratory({ auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const laboratoryId = await this.getLaboratoryIdByPersonal(auth);
      if (!laboratoryId) {
        apiResponse.message = "Laboratory not found";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const entity = await Laboratory.query().where('id', laboratoryId).first();
      if (!entity) {
        apiResponse.message = "Laboratory not found";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const wallet = await Wallet.query().where('owner_id', entity.id).where('owner_type', 'laboratory').first();
      apiResponse = {
        success: true,
        message: "Details de l'entité laboratoire retrouvée avec succès",
        result: {
          entity: entity,
          wallet: wallet
        }
      }
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Erreur lors de la récupération des informations de l'entité laboratoire",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getEntityPersonals({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const laboratoryId = await this.getLaboratoryIdByPersonal(auth);

      if (!laboratoryId) {
        apiResponse.message = "Laboratory not found";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const personals = await Laborantin.query().where('laboratory_id', laboratoryId)
        .preload('country').preload('city').preload('district')
        .paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des personnels de l'entité laboratoire retrouvée avec succès",
        result: personals
      }

    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Erreur lors de la récupération des informations de l'entité personnel",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async addNewPersonal({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          last_name: schema.string(),
          first_name: schema.string(),
          email: schema.string.nullable({ trim: true }, [
            rules.email()
          ]),
          phone: schema.string(),
          country_id: schema.number(),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          gender: schema.enum(["M", "F"]),
          address: schema.string.optional(),
          profession: schema.string.optional(),
          role: schema.enum.optional(['manager', 'laborantin', 'caisse', 'comptable']),
          password: schema.string(),
        })
      });

      const { last_name, first_name, email, phone, country_id, birthday_year, birthday_month, birthday_day, gender, address, profession, role, password } = payload;

      const laboratoryId = await this.getLaboratoryIdByPersonal(auth);
      if (!laboratoryId) {
        apiResponse = {
          success: false,
          message: "Echec, vous n'avez pas accès à cette fonctionnalité",
          result: null as any,
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const check_exist_user = await User.query().where('phone', phone).orWhere('email', String(email)).first();

      if (check_exist_user) {
        apiResponse = {
          success: false,
          message: "Echec, le compte existe déjà avec cet email ou ce numéro de téléphone",
          result: null as any,
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const trx = await Database.transaction();
      try {
        let codeP = await this.generateCodeParrainage(8);
        const codeParrainage = {
          create_account: 0,
          active_qrcode: 0,
          adhesion_fees: 0,
          plan: 1,
          activeMoney: false
        }
        let username = `${last_name} ${first_name}`;
        const user = await User.create({
          username: username,
          phone: phone,
          email: email,
          password: password,
          countryId: country_id,
          roleId: 5,
          status: UserStatus.Actived,
          codeParrainage: codeP,
          parrainage: JSON.stringify(codeParrainage)
        }, { client: trx });

        if (!user) {
          apiResponse = {
            success: false,
            message: "Echec de création du compte utilisateur",
            result: null,
          }
          status = 500;
          await trx.rollback();
          return response.status(status).json(apiResponse);
        }
        const code = await this.generateToken();

        const laborantin = await Laborantin.create({
          userId: user.id,
          lastName: last_name,
          firstName: first_name,
          phone: phone,
          email: email,
          countryId: country_id,
          gender: gender,
          birthdayYear: birthday_year,
          birthdayMonth: birthday_month,
          birthdayDay: birthday_day,
          code: code,
          status: 'validated',
          laboratory_id: laboratoryId,
          profession: profession,
          address: address,
          role: role,
        }, { client: trx });

        if (!laborantin) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Une erreur serveur est survenue lors de la création du compte, veuillez réessayer plus tard",
            result: null,
            except: laborantin,
          }
          status = 500;
          return response.status(status).json(apiResponse);
        }
        await trx.commit();
        apiResponse = {
          success: true,
          message: "Compte laborantin créé avec succès",
          result: {
            laborantin: laborantin,
            user: user
          },
        }
        status = 201;
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        apiResponse = {
          success: false,
          message: "Une erreur serveur est survenue lors de la création du compte, veuillez réessayer plus tard",
          result: null,
          except: error.message,
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de l'enregistrement du personnel";
      apiResponse.success = false;
      apiResponse.except = error.message;
      return response.status(status).json(apiResponse);
    }
  }

  public async getLatestQuotationRequests({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;

    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        apiResponse.message = "You are not authorized to access this resource";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const laboratoryId = personal.laboratory_id;

      // Utilisation d'une jointure pour récupérer les quotation_requests
      const quotationRequests = await QuotationRequest
        .query()
        .select('quotation_requests.*') // Sélectionner uniquement les colonnes de quotation_requests
        .join('quotation_partners', 'quotation_requests.id', 'quotation_partners.quotation_request_id')
        .where('quotation_partners.laboratory_id', laboratoryId)
        // Correction pour MySQL (au lieu de INTERVAL '1 day')
        .where('quotation_partners.notified_at', '>=', Database.raw('DATE_SUB(NOW(), INTERVAL 1 DAY)'))
        .whereIn('quotation_partners.status', ['pending', 'partially_end'])
        .whereIn('quotation_requests.status', ['in_progress','partially_paid'])
        .whereNotExists((query) => {
          query.from('quotation_proposals as qp2')
            .whereRaw('qp2.quotation_request_id = quotation_requests.id')
            .whereRaw('qp2.version_id = quotation_requests.current_version')
            .andWhere('qp2.laboratory_id', laboratoryId);
        })
        .preload('patient')
        .preload('analyzeAsk', (query) => {
          query.preload('items', (query) => {
            query.preload('analyze');
          });
        })
      .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Quotation requests fetched successfully",
        result: quotationRequests,
      };
    } catch (error) {
      console.error("Error fetching quotation requests:", error);
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }

    return response.status(status).json(apiResponse);
  }

  public async updateLaboratoryLocation({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          location: schema.object().members({
            latitude: schema.number(),
            longitude: schema.number(),
          })
        })
      });
      const { location } = payload;
      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        status = 401;
        apiResponse.message = "Veuillez vous authentifié pour continuer l'opération";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }
      const laboratoryId = personal.laboratory_id;
      const laboratory = await Laboratory.query().where('id', laboratoryId).first();
      if (!laboratory) {
        status = 404;
        apiResponse.message = "Le compte est introuvable";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }

      await laboratory.merge({
        location: JSON.stringify({ lat: location.latitude, long: location.longitude }),
      }).save();

      apiResponse = {
        success: true,
        message: "La position géographique de la laboratoire a été mise à jour avec succès",
        result: laboratory,
      }
      return response.status(status).json(apiResponse);

    } catch (error) {
      console.log("error in update laboratory", error);
      apiResponse = {
        success: false,
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        result: null,
        except: error.message,
        errors: error.messages
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async getEntityTransactions({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const walletEntity = await Wallet.query().where('owner_id', entityId).where('owner_type', 'laboratory').andWhere('type_wallet_id', 3).first();
      if (walletEntity === null) {
        apiResponse.message = "Portefeuille de l'entité introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const transactions = await WalletHistory.query().where('wallet_id', walletEntity.id).orderBy('created_at', 'desc')
        .preload('transaction', function (query) {
          query.select(['beneficiary_id', 'paid_by_self', 'amount', 'payment_type_id', 'description', 'date_op', 'status', 'metadata', 'trx_ref', 'created_at', 'updated_at'])
          query.preload('beneficiary').preload('paymentType')
        })
        .preload('wallet')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Données récupérées",
        result: transactions,
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur serveur est survenue, veuillez réessayer plus tard";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getAnalyticalData({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;
    try {
      const LaboratoryId = await this.getLaboratoryIdByPersonal(auth);
      if (LaboratoryId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const [proposalAccepted, proposalRejected, quotationRequests, orders] = await Promise.all([
        Database.query().from('quotation_proposals')
          .where('laboratory_id', LaboratoryId)
          .where('status', 'approved')
          .count('* as total'),
        Database.query().from('quotation_partners')
          .where('laboratory_id', LaboratoryId)
          .whereIn('status', ['rejected', 'expired'])
          .count('* as total'),
        Database.query().from('quotation_partners')
          .where('laboratory_id', LaboratoryId)
          .whereIn('status', ['pending', 'accepted', 'completed','partially_end'])
          .count('* as total'),
        Database.query().from('orders')
          .where('laboratory_id', LaboratoryId)
          .count('* as total'),
      ]);

      // Extraire les valeurs de comptage des résultats
      const totalProposalAccepted = parseInt(proposalAccepted[0].total || "0", 10);
      const totalProposalRejected = parseInt(proposalRejected[0].total || "0", 10);
      const totalQuotationRequest = parseInt(quotationRequests[0].total || "0", 10);
      const totalOrder = parseInt(orders[0].total || "0", 10);

      // Construire la réponse réussie
      apiResponse = {
        success: true,
        message: "Etat analytique",
        result: {
          totalProposalAccepted,
          totalProposalRefused: totalProposalRejected,
          totalQuotationRequest,
          totalOrder,
        },
      };

    } catch (error) {
      status = 500;
      console.error("Une erreur est survenue :", error);

      // Construire la réponse en cas d'erreur
      apiResponse = {
        success: false,
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        result: null,
        except: error.message,
      };
    }
    return response.status(status).json(apiResponse);
  }

  public async getAnalyzeAskPendingResult({request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;
    try {
      const page = request.input('page',1);
      const limit = request.input('limit',10);

      const LaboratoryId = await this.getLaboratoryIdByPersonal(auth);
      if (LaboratoryId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const analyzeAsks = await AnalyzeAsk.query()
        .where('result_is_defined', false)
        .whereIn('result_status', ['pending','partially'])
        .where('is_requested', true)
        .whereIn('payment_status', ['paid', 'partially_paid'])
        .preload('quotation_request', function (query) {
          query.preload('orders', function (orderQ){
            orderQ.where('laboratory_id', LaboratoryId).where('status','paid').whereNotNull('analyze_ask_id')
            orderQ.preload('items')
            orderQ.orderBy('created_at', 'desc')
          })
          query.preload('proposals', function (proposalQ){
            proposalQ.select(['id', 'quotation_request_id', 'laboratory_id', 'total_price', 'total_assured_price', 'total_items', 'status', 'created_at', 'updated_at'])
            proposalQ.where('laboratory_id', LaboratoryId).where('status','approved')
            proposalQ.orderBy('created_at', 'desc')
          })
        })
        .preload('diagnostic', function (dQuery) {
          dQuery.select(['id', 'reference', 'libelle', 'content', 'symptoms',])
        })
        .preload('patient', function (pQuery) {
          pQuery.select(['id', 'first_name', 'last_name', 'phone', 'email'])
        })
        .preload('pro', function (pQuery) {
          pQuery.select(['id', 'first_name', 'last_name', 'phone', 'email'])
        })
        .withCount('items')
        .orderBy('created_at', 'desc')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des analyses à traiter",
        result: analyzeAsks,
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur serveur est survenue, veuillez réessayer plus tard";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getAnalyzeResultByAnalyzeAsk({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;
    try {
      const analyzeAskId = request.input('analyze_ask_id');
      if (!analyzeAskId) {
        apiResponse.message = "Veuillez renseignez l'identifiant de la demande d'analyse";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const LaboratoryId = await this.getLaboratoryIdByPersonal(auth);
      if (LaboratoryId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const analyze_sk = await AnalyzeAsk.query().where('id', analyzeAskId).first();
      if (!analyze_sk) {
        apiResponse.message = "Demande d'analyse introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const analyze_ask_result = await AnalyzeAskResult.query().where('analyze_ask_id', analyzeAskId).andWhere('laboratory_id', LaboratoryId)
        .preload('items')
      .first();

      apiResponse = {
        success: true,
        message: "Résultat de l'analyse",
        result: analyze_ask_result,
      }

    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur serveur est survenue, veuillez réessayer plus tard";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }
}
