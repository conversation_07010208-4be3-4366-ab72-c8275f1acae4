import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class CodeParrainage extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'code' })
  public code: string

  @column({ columnName: 'activated_qrcode_compteur' })
  public activatedQrcodeCompteur: number | null

  @column({ columnName: 'compteur' })
  public compteur: number

  @column({ columnName: 'ca_adhesion_compteur' })
  public caAdhesionCompteur: number

  @column({ columnName: 'plan' })
  public plan: number | null;

  @column({ columnName: 'active_money' })
  public activeMoney: boolean
}
