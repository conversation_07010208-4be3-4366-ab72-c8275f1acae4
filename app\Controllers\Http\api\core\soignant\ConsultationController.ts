import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator';
import { ApiResponse } from 'App/Controllers/Utils/models';
import AnalyzeAsk from 'App/Models/AnalyzeAsk';
import Diagnostic from 'App/Models/Diagnostic';
import Patient from 'App/Models/Patient';
import Prescription from 'App/Models/Prescription';
import Soignant from 'App/Models/Soignant';
import HelperController from '../../helpers/HelperController';
import Database from '@ioc:Adonis/Lucid/Database';
import { MultipartFileContract } from '@ioc:Adonis/Core/BodyParser';
import VitalParameter from 'App/Models/VitalParameter';
import PrescriptionItem from 'App/Models/PrescriptionItem';
import QuotationRequest from 'App/Models/QuotationRequest';
import HealthInstitute from 'App/Models/HealthInstitute';
import QuotationPartner from 'App/Models/QuotationPartner';
import Product from 'App/Models/Product';
import AnalyzeAskItem from 'App/Models/AnalyzeAskItem';
import Pathology from 'App/Models/Pathology';
import { DateTime } from 'luxon';
import InsuranceCompanyPrescription from 'App/Models/InsuranceCompanyPrescription';
import InsuranceCompanyPrescriptionItem from 'App/Models/InsuranceCompanyPrescriptionItem';
import InsuranceCompanyAnalyzeAsk from 'App/Models/InsuranceCompanyAnalyzeAsk';
import InsuranceCompanyAnalyzeAskItem from 'App/Models/InsuranceCompanyAnalyzeAskItem';

export default class ConsultationController extends HelperController {

  public async getDiagnostics({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pro = await Soignant.query().where('user_id', authUser.id).first();
      if (!pro) {
        apiResponse.message = "Personnel introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const filters = {
        patient_id: request.input('patient_id') || null,
        health_institute_id: request.input('health_institute_id') || null,
        category_diagnostic_id: request.input('category_diagnostic_id') || null,
        pathology_id: request.input('pathology_id') || null,
      }

      let queries = Diagnostic.query().where('pro_id', pro.id).withCount('prescriptions').orderBy('created_at', 'desc')
        .preload('patient').preload('healthInstitute').preload('category').preload('pathology').preload('leastPathology')

      if (filters) {
        Object.keys(filters).forEach(key => {
          if (filters[key] && filters[key] !== null) {
            queries = queries.where(key, filters[key]);
          }
        });
      }

      const diagnostics = await queries.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Diagnostics",
        result: diagnostics,
      };
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.except = error;
      status = 500;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  public async getPrescriptions({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pro = await Soignant.query().where('user_id', authUser.id).first();
      if (!pro) {
        apiResponse.message = "Personnel introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const prescriptions = await Prescription.query().where('pro_id', pro.id)
        .preload('diagnostic')
        .preload('patient')
        .preload('items')
        .orderBy('created_at', 'desc')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Prescriptions",
        result: prescriptions,
      };
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.except = error;
      status = 500;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  public async getAnalyzeAskByPro({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pro = await Soignant.query().where('user_id', authUser.id).first();
      if (!pro) {
        apiResponse.message = "Personnel introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const analyzeAsks = await AnalyzeAsk.query().where('pro_id', pro.id)
        .preload('diagnostic')
        .preload('patient')
        .preload('items')
        .orderBy('created_at', 'desc')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des analyses demandées",
        result: analyzeAsks,
      };
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.except = error;
      status = 500;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  // Méthode pour gérer l'upload des fichiers de diagnostic
  private async handleFiles(files: (MultipartFileContract | undefined)[] | undefined) {
    const fileData = [] as string[];
    if (files) {
      for (const file of files) {
        if (file) {
          const fileName = `${Date.now()}-${file.clientName}.${file.extname}`;
          fileData.push(fileName);
        }
      }
    }
    return fileData;
  }

  public async createPatientDiagnostic({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number(),
          health_institute_id: schema.number.optional(),
          category_diagnostic_id: schema.number.optional(),
          pathology_id: schema.number.optional(),
          least_pathology_id: schema.number.optional(),
          other_pathology: schema.array.optional().members(schema.object().members({
            name: schema.string.optional(),
            code: schema.string.optional(),
          })),
          libelle: schema.string(),
          content: schema.string(),
          symptoms: schema.array().members(schema.object().members({
            name: schema.string(),
            code: schema.string.optional(),
          })),
          examens: schema.string.optional(),
          cat: schema.string.optional(),
          consultation_price: schema.number.optional(),
          files: schema.array.optional().members(
            schema.file.optional({
              size: '12mb',
              extnames: ['jpg', 'png', 'jpeg', 'pdf'],
            })
          ),
          vital_signs: schema.array.optional().members(
            schema.object().members({
              libelle: schema.string(),
              value: schema.string(),
              vital_exam_id: schema.number(),
              unit: schema.string.optional(),
            })
          ),
        }),
        messages: {
          patient_id: 'Le champ patient_id est obligatoire',
          health_institute_id: 'Le champ health_institute_id est obligatoire',
          category_diagnostic_id: 'Le champ category_diagnostic_id est obligatoire',
          pathology_id: 'Le champ pathology_id est obligatoire',
          least_pathology_id: 'Le champ least_pathology_id est obligatoire',
          libelle: 'Le champ libelle est obligatoire',
          content: 'Le champ content est obligatoire',
          symptoms: 'Le champ symptoms est obligatoire',
          vital_signs: 'Le champ vital_signs est obligatoire',
        }
      });

      const {
        patient_id, health_institute_id, category_diagnostic_id, pathology_id, least_pathology_id, libelle, content,
        symptoms, examens, cat, consultation_price, files, vital_signs,other_pathology,
      } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pro = await Soignant.query().where('user_id', authUser.id).first();
      if (!pro) {
        apiResponse.message = "Personnel introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const patient = await Patient.query().where('id', patient_id).first();
      if (!patient) {
        apiResponse.message = "Patient introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      // Validation de l'accès au carnet de santé du patient
      const checkPatient = await this.checkPatientIsValid(patient);
      if (!checkPatient.success) {
        apiResponse.message = checkPatient.message;
        return response.status(404).json(apiResponse);
      }

      // Validation de l'accès aux diagnostics du patient
      const checkAccess = await this.checkPatientDiagnosticAccess(patient, pro);
      if (!checkAccess.success) {
        apiResponse.message = "Vous n'avez pas accès au carnet de santé numérique de ce patient";
        return response.status(404).json(checkAccess);
      }

      const checkMostPathology = await Pathology.query().where('id', Number(pathology_id)).first();


      const trx = await Database.transaction();
      try {
        // Création du diagnostic
        const fileData = await this.handleFiles(files);
        const reference = Math.floor(Math.random() * **********000) + **********000;

        const diagnostic = await Diagnostic.create({
          proId: pro.id,
          patientId: patient_id,
          healthInstituteId: health_institute_id,
          categoryDiagnosticId: category_diagnostic_id,
          pathologyId: checkMostPathology?.id,
          leastPathologyId: least_pathology_id,
          otherPathology: other_pathology,
          libelle: libelle,
          content: content,
          symptoms: symptoms,
          examens: examens,
          cat: cat,
          consultationPrice: consultation_price,
          files: fileData.length > 0 ? fileData : null,
          reference: reference.toString(),
          startAt: new Date(),
        }, { client: trx });

        if (!diagnostic) {
          await trx.rollback();
          apiResponse.message = 'Diagnostic non créé';
          apiResponse.except = diagnostic;
          return response.status(400).json(apiResponse);
        }

        // Ajout des signes vitaux (si présents)
        if (vital_signs?.length) {
          const vitalParams = vital_signs.map(vitalSign => ({
            vitalExamId: vitalSign.vital_exam_id,
            patientId: patient_id,
            libelle: vitalSign.libelle,
            value: vitalSign.value,
            unit: vitalSign.unit,
            diagnosticId: diagnostic.id,
          }));
          await VitalParameter.createMany(vitalParams, { client: trx });
        }

        await trx.commit();
        apiResponse = {
          message: "Diagnostic créé avec succès",
          success: true,
          result: diagnostic,
        };
        return response.status(status).json(apiResponse);
      } catch (error) {
        console.log("error in create diagnostic", error.message)
        await trx.rollback();
        apiResponse = {
          message: "Une erreur est survenue lors de la création de Diagnostic",
          success: false,
          result: null as any,
          except: error
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }

    } catch (error) {
      console.log("error in createPatientDiagnostic", error.message);
      apiResponse = {
        message: "Une erreur est survenue lors de la création de Diagnostic",
        success: false,
        result: null as any,
        except: error
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async addPrescriptionToDiagnostic({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    };
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          diagnostic_id: schema.number(),
          patient_id: schema.number.optional(),
          name: schema.string.optional(),
          reason: schema.string.optional(),
          items: schema.array().members(
            schema.object().members({
              product_id: schema.number(),
              substitutable_product_id: schema.number.optional(),
              quantity: schema.number(),
              dosage: schema.string.optional(),
              variant: schema.string.optional(),
              posologie: schema.string.optional(),
              frequency: schema.string.optional(),
              duration: schema.string.optional(),
              is_substitutable: schema.boolean.optional(),
              is_insured: schema.boolean.optional(),
              insurance_company_id: schema.number.optional(),
              insurance_company_subscription_id: schema.number.optional(),
              patient_insurance_company_id: schema.number.optional(),
              package_product_id: schema.number.optional(),
              public_price: schema.number.optional(),
            })
          ),
          create_quotation: schema.boolean.optional(),
          priority: schema.enum.optional(['urgent', 'normal', 'low']),
          comment: schema.string.optional(),
          location: schema.object.optional().members({
            libelle: schema.string.optional(),
            description: schema.string.optional(),
            lat: schema.string(),
            long: schema.string(),
          }),

        }),
      });
      const { diagnostic_id, patient_id, name, reason, items, create_quotation, priority, comment, location } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const userId = authUser.id;

      const diagnostic = await Diagnostic.query().where('id', diagnostic_id).orWhere('reference', diagnostic_id).first();
      if (!diagnostic) {
        apiResponse.message = "Diagnostic non trouvé";
        return response.status(404).json(apiResponse);
      }

      // Récupérer le soignant et valider ses droits
      const soignant = await Soignant.query().where('user_id', userId).first();
      if (!soignant) {
        apiResponse.message = 'Vous n\'êtes pas soignant';
        return response.status(401).json(apiResponse);
      }

      // Vérifier si le patient existe
      let patientId = patient_id ?? diagnostic.patientId;
      const patient = await Patient.query().where('id', patientId).first();
      if (!patient) {
        apiResponse.message = 'Vous n\'avez pas accès à ce patient';
        return response.status(404).json(apiResponse);
      }
      // Validation de l'accès aux diagnostics du patient
      const checkAccess = await this.checkPatientDiagnosticAccess(patient, soignant);
      if (!checkAccess.success) {
        apiResponse.message = "Vous n'avez pas accès au carnet de santé numérique de ce patient";
        return response.status(404).json(checkAccess);
      }
      // Création de la prescription
      let trx = await Database.transaction();
      try {
        let codeRef = Math.floor(Math.random() * **********) + **********;
        let desc = name ?? "Prescription du " + new Date().toLocaleDateString();

        // Création de la prescription principale
        const prescription = await Prescription.create({
          diagnosticId: diagnostic.id,
          patientId: patient.id,
          name: desc,
          reason: reason,
          proId: soignant.id,
          codeRef: codeRef,
          usedInsurance: items.some(item => item.is_insured ?? false),
        }, { client: trx });

        if (!prescription) {
          await trx.rollback();
          apiResponse.message = 'Prescription non créée';
          return response.status(400).json(apiResponse);
        }

        // Ajout des produits à la prescription
        let data = [] as any[];
        for (const item of items) {
          let newItem = {
            prescriptionId: prescription.id,
            productId: item.product_id,
            substitutableProductId: item.substitutable_product_id,
            quantity: item.quantity,
            dosage: item.dosage,
            variant: item.variant,
            posologie: item.posologie,
            frequency: item.frequency,
            duration: item.duration,
            isSubstitutable: item.is_substitutable ?? false,
            used_insurance: item.is_insured ?? false,
          }
          data.push(newItem);
        }

        const prescriptItems = await PrescriptionItem.createMany(data, { client: trx });
        if (!prescriptItems) {
          await trx.rollback();
          apiResponse.message = 'Prescriptions non créée';
          return response.status(400).json(apiResponse);
        }

        // Grouper les items par assurance
        const insuredItems = prescriptItems.filter(item => item.used_insurance);
        const itemsByInsurance = insuredItems.reduce((acc, item) => {
          const insuranceItem = items.find(i => i.product_id === item.productId);
          if (!insuranceItem) return acc;

          const insuranceId = insuranceItem.insurance_company_id;
          if (!insuranceId) return acc;

          if (!acc[insuranceId]) {
            acc[insuranceId] = {
              items: [],
              totalQuantity: 0,
              totalAmount: 0,
              insuranceData: {
                insurance_company_id: insuranceId,
                patient_insurance_company_id: insuranceItem.patient_insurance_company_id ?? null,
                subscription_id: insuranceItem.insurance_company_subscription_id ?? null,
              }
            };
          }

          acc[insuranceId].items.push(item);
          acc[insuranceId].totalQuantity += item.quantity;
          acc[insuranceId].totalAmount += (Number(insuranceItem?.public_price || 0) * item.quantity);

          return acc;
        }, {} as Record<number, {
          items: typeof insuredItems,
          totalQuantity: number,
          totalAmount: number,
          insuranceData: {
            insurance_company_id: number,
            patient_insurance_company_id: number | null,
            subscription_id: number | null,
          }
        }>);

        // Créer les prescriptions d'assurance
        for (const [, data] of Object.entries(itemsByInsurance)) {
          const insurancePrescription = await InsuranceCompanyPrescription.create({
            patientId: patient.id,
            insuranceCompanyId: data.insuranceData.insurance_company_id,
            patientInsuranceCompanyId: Number(data.insuranceData.patient_insurance_company_id),
            insuranceCompanySubscriptionId: Number(data.insuranceData.subscription_id),
            diagnosticId: diagnostic.id,
            prescriptionId: prescription.id,
            isActive: true,
            totalItemsAssured: data.totalQuantity,
            totalAmountAssured: data.totalAmount,
            status: 'pending',
            validatedBy: null,
            notes: null,
            rejectedReason: null
          }, { client: trx });

          if (!insurancePrescription) {
            await trx.rollback();
            apiResponse.message = 'Erreur lors de la création de la prescription d\'assurance';
            return response.status(400).json(apiResponse);
          }

          // Créer les items de prescription d'assurance avec createMany
          const insurancePrescriptionItemsData = data.items.map(item => {
            const insuranceItem = items.find(i => i.product_id === item.productId);
            return {
              patientId: patient.id,
              patientCompanyAssuranceId: Number(data.insuranceData.patient_insurance_company_id),
              insuranceCompanyPrescriptionId: insurancePrescription.id,
              prescriptionItemId: item.id,
              isActive: true,
              totalQuantityAssured: item.quantity,
              totalAmountAssured: Number(insuranceItem?.public_price || '0') * item.quantity,
              status: 'pending' as 'pending',
            };
          });

          if (insurancePrescriptionItemsData.length > 0) {
            await InsuranceCompanyPrescriptionItem.createMany(insurancePrescriptionItemsData, { client: trx });
          }
        }

        // Gestion de la demande de devis (reste inchangé)
        if (create_quotation) {
          let total_items = 0;
          if (prescription && prescriptItems.length > 0) {
            total_items = prescriptItems.reduce((acc, item) => acc + item.quantity, 0);
          }

          const quotationRequest = await QuotationRequest.create({
            reference: codeRef.toString(),
            requestedBy: userId,
            patientId: patient.id,
            prescriptionId: Number(prescription?.id),
            typeRequest: 'prescription',
            typeOrder: 0,
            priority: priority as 'urgent' | 'normal' | 'low',
            comment: comment ?? '',
            isActive: true,
            status: 'pending' as 'pending',
            location: JSON.stringify(location),
            totalItems: total_items
          }, { client: trx });

          if (!quotationRequest) {
            await trx.rollback();
            apiResponse.message = "La demande de devis n'a pas été créée avec succès";
            status = 400;
            return response.status(status).json(apiResponse);
          }

          await prescription.useTransaction(trx).merge({
            isRequested: true,
          }).save();

          const healthInstitute = await HealthInstitute.query()
            .where('id', soignant.healthInstituteId)
            .preload('pharmacy')
            .first();

          if (healthInstitute) {
            const pharmacy = healthInstitute.pharmacy;
            if (pharmacy) {
              const quotationPartner = await QuotationPartner.create({
                pharmacyId: pharmacy.id,
                quotationRequestId: quotationRequest.id,
                isNotified: true,
                status: 'pending' as const,
                notifiedAt: DateTime.now(),
              });

              if (!quotationPartner) {
                apiResponse.message = "La demande de devis n'a pas été créée avec succès";
                status = 400;
                return response.status(status).json(apiResponse);
              }

              await trx.commit();
              apiResponse = {
                success: true,
                message: 'Prescription créée avec succès',
                result: {
                  prescription: prescription,
                  items: prescriptItems,
                  quotationRequest: quotationRequest,
                  insurancePrescriptions: Object.values(itemsByInsurance).map(d => d.insuranceData)
                },
              };
              return response.status(200).json(apiResponse);
            }
          }
        }

        await trx.commit();
        apiResponse = {
          success: true,
          message: 'Prescription créée avec succès',
          result: {
            prescription: prescription,
            items: prescriptItems,
            insurancePrescriptions: Object.values(itemsByInsurance).map(d => d.insuranceData)
          }
        };
        return response.status(status).json(apiResponse);
      } catch (error) {
        console.log("error", error);
        await trx.rollback();
        status = 500;
        apiResponse = {
          success: false,
          message: "Echec de création de la prescription",
          result: null,
          except: error.message,
        }
      }
    } catch (error) {
      status = 500;
      console.log("error in add prescription", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        except: error.messages,
      }
    }
    return response.status(status).json(apiResponse);
  }

  /**
   * addSubstitutableProduct function
   * @description ajouter un produit substituable à une prescription
   * @param param0
   * @returns
   */
  public async addSubstitutableProduct({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    };
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          prescriptionItemId: schema.number(),
          productId: schema.number(),
        }),
      });
      const { prescriptionItemId, productId } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const userId = Number(authUser.id);
      if (!userId) {
        apiResponse.message = "Vous n'êtes pas autorisé à effectuer cette action";
        return response.status(401).json(apiResponse);
      }

      // Vérifier si le produit à remplacer existe
      const product = await Product.query().where('id', productId).first();
      if (!product) {
        apiResponse.message = "Le produit spécifié n'existe pas";
        return response.status(404).json(apiResponse);
      }

      // Récupérer le produit à remplacer
      const prescriptionItem = await PrescriptionItem.query().where('id', prescriptionItemId).first();
      if (!prescriptionItem) {
        apiResponse.message = "Item de prescription non trouvé";
        return response.status(401).json(apiResponse);
      }

      // Vérifier si le produit à remplacer est substituable
      if (!prescriptionItem.isSubstitutable) {
        apiResponse.message = "Ce produit ne peut pas être remplacé";
        return response.status(400).json(apiResponse);
      }

      // Ajouter le produit substituable
      const up = await PrescriptionItem.query().where('id', prescriptionItemId).update({
        substitutableProductId: productId,
        isSubstitutable: true,
      });

      apiResponse = {
        success: true,
        message: 'Produit substituable ajouté avec succès',
        result: { up }
      };

    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de l'ajout du produit substituable",
        result: null,
        except: error.message,
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async createDiagnosticAnalyzeAsk({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    };
    let status = 201;

    try {
      const payload = await request.validate({
        schema: schema.create({
          diagnostic_id: schema.number(),
          patient_id: schema.number.optional(),
          reason: schema.string.optional(),
          items: schema.array().members(
            schema.object().members({
              analyze_id: schema.number(),
              reason: schema.string.optional(),
              is_insured: schema.boolean.optional(),
              package_analyze_id: schema.number.optional(),
              insurance_company_id: schema.number.optional(),
              insurance_company_subscription_id: schema.number.optional(),
              patient_insurance_company_id: schema.number.optional(),
              public_price: schema.string.optional(),
            })
          ),
        }),
      });

      const { diagnostic_id, patient_id, reason, items } = payload;
      const authUser = await auth.authenticate();

      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const soignant = await Soignant.query().where('user_id', authUser.id).first();
      if (!soignant) {
        apiResponse.message = 'Vous n\'êtes pas soignant';
        return response.status(401).json(apiResponse);
      }

      const diagnostic = await Diagnostic.query()
        .where('id', diagnostic_id)
        .orWhere('reference', diagnostic_id)
        .first();

      if (!diagnostic) {
        apiResponse.message = "Diagnostic non trouvé";
        return response.status(404).json(apiResponse);
      }

      let patientId = patient_id ?? diagnostic.patientId;
      const patient = await Patient.query().where('id', patientId).first();

      if (!patient) {
        apiResponse.message = 'Vous n\'avez pas accès à ce patient';
        return response.status(404).json(apiResponse);
      }

      const checkAccess = await this.checkPatientDiagnosticAccess(patient, soignant);
      if (!checkAccess.success) {
        apiResponse.message = "Vous n'avez pas accès au carnet de santé numérique de ce patient";
        return response.status(404).json(checkAccess);
      }

      const trx = await Database.transaction();

      try {
        // Création de la demande d'analyse principale
        let codeRef = Math.floor(Math.random() * **********) + **********;
        const analyze_ask = await AnalyzeAsk.create({
          diagnosticId: diagnostic.id,
          patientId: patient.id,
          proId: soignant.id,
          reason: reason,
          reference: codeRef.toString(),
          usedInsurance: items.some(item => item.is_insured ?? false),
        }, { client: trx });

        if (!analyze_ask) {
          await trx.rollback();
          apiResponse.message = "Erreur lors de la création de la demande d'analyse";
          return response.status(500).json(apiResponse);
        }

        // Création des items d'analyse
        const analyzeAskItems = await AnalyzeAskItem.createMany(
          items.map(item => ({
            analyzeAskId: analyze_ask.id,
            analyzeId: item.analyze_id,
            diagnosticId: diagnostic.id,
            usedInsurance: item.is_insured ?? false,
          })),
          { client: trx }
        );

        if (!analyzeAskItems) {
          await trx.rollback();
          apiResponse.message = "Erreur lors de la création des items d'analyse";
          return response.status(500).json(apiResponse);
        }

        // Grouper les items par assurance
        const insuredItems = analyzeAskItems.filter(item => item.usedInsurance);
        const itemsByInsurance = insuredItems.reduce((acc, item) => {
          const insuranceItem = items.find(i => i.analyze_id === item.analyzeId);
          if (!insuranceItem || !insuranceItem.insurance_company_id) return acc;

          const insuranceId = insuranceItem.insurance_company_id;

          if (!acc[insuranceId]) {
            acc[insuranceId] = {
              items: [],
              totalAmount: 0,
              insuranceData: {
                insurance_company_id: insuranceId,
                patient_insurance_company_id: insuranceItem.patient_insurance_company_id ?? null,
                subscription_id: insuranceItem.insurance_company_subscription_id ?? null,
              }
            };
          }

          acc[insuranceId].items.push(item);
          acc[insuranceId].totalAmount += parseFloat(insuranceItem.public_price || '0');

          return acc;
        }, {} as Record<number, {
          items: typeof insuredItems,
          totalAmount: number,
          insuranceData: {
            insurance_company_id: number,
            patient_insurance_company_id: number | null,
            subscription_id: number | null
          }
        }>);

        // Créer les demandes d'analyse pour chaque assurance
        for (const [, data] of Object.entries(itemsByInsurance)) {
          const insuranceAnalyzeAsk = await InsuranceCompanyAnalyzeAsk.create({
            patientId: patient.id,
            insuranceCompanyId: data.insuranceData.insurance_company_id,
            patientInsuranceCompanyId: Number(data.insuranceData.patient_insurance_company_id),
            insuranceCompanySubscriptionId: Number(data.insuranceData.subscription_id),
            diagnosticId: diagnostic.id,
            analyzeAskId: analyze_ask.id,
            totalAmountAssured: data.totalAmount,
            status: 'pending',
            validatedBy: null,
            notes: null,
            rejectedReason: null
          }, { client: trx });

          if (!insuranceAnalyzeAsk) {
            await trx.rollback();
            apiResponse.message = 'Erreur lors de la création de la demande d\'analyse d\'assurance';
            return response.status(400).json(apiResponse);
          }

          // Création des items d'analyse assurés
          const insuranceAnalyzeItemsData = data.items.map(item => {
            const insuranceItem = items.find(i => i.analyze_id === item.analyzeId);
            return {
              patientId: patient.id,
              patientCompanyAssuranceId: Number(data.insuranceData.patient_insurance_company_id),
              insuranceCompanyAnalyzeAskId: insuranceAnalyzeAsk.id,
              analyzeAskItemId: item.id,
              isActive: true,
              totalAmountAssured: parseFloat(insuranceItem?.public_price || '0'),
              totalQuantityAssured: 1,
              status: 'pending' as 'pending',
            };
          });

          if (insuranceAnalyzeItemsData.length > 0) {
            await InsuranceCompanyAnalyzeAskItem.createMany(insuranceAnalyzeItemsData, { client: trx });
          }
        }

        await trx.commit();

        apiResponse = {
          success: true,
          message: "Demande d'analyse créée avec succès",
          result: {
            analyzeAsk: analyze_ask,
            items: analyzeAskItems,
            insuranceAnalyzeAsks: Object.values(itemsByInsurance).map(d => d.insuranceData)
          }
        };
      } catch (error) {
        console.log("error", error.message);
        await trx.rollback();
        status = 500;
        apiResponse = {
          success: false,
          message: "Erreur lors de la création de la demande d'analyse",
          result: null,
          except: error.message,
          errors: error.messages,
        };
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Erreur lors du traitement de la demande",
        result: null,
        except: error.message,
        errors: error.messages,
      };
    }

    return response.status(status).json(apiResponse);
  }

  public async getDiagnosticDetails({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    let status = 200;
    try {
      const diagnosticId = request.input('reference');
      if (!diagnosticId) {
        apiResponse.message = "La consultation n'est pas valide";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const soignant = await Soignant.query().where('user_id', authUser.id).first();
      if (!soignant) {
        apiResponse.message = 'Vous n\'êtes pas soignant';
        return response.status(401).json(apiResponse);
      }
      const diagnostic = await Diagnostic.query().where('id', diagnosticId).orWhere('reference', diagnosticId)
        .preload('category').preload('pathology').preload('leastPathology')
        .preload('patient')
        .preload('healthInstitute', function (query) {
          query.preload('country').preload('city').preload('quarter').preload('typeHealthInstitute')
        })
        .preload('prescriptions', function (query) {
          query.orderBy('created_at', 'desc').preload('items', function (q) {
            q.preload('product').preload('substitutableProduct')
          })
        })
        .preload('analyze_asks', function (query) {
          query.orderBy('created_at', 'desc').preload('items', function (q) {
            q.preload('analyze')
          })
        })
        .first();
      if (!diagnostic) {
        apiResponse.message = "Diagnostic introuvable";
        return response.status(404).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Diagnostic",
        result: diagnostic,
      }
    } catch (error) {
      console.log("Error in get diagnostic details", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Une erreur est survenue lors de l'analyse de votre demande",
        result: null,
        except: error.message,
      }
    }
    return response.status(status).json(apiResponse);
  }


}
