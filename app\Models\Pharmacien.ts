import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Pharmacy from './Pharmacy'
import User from './User'
import Country from './Country'
import City from './City'
import Quarter from './Quarter'

export default class Pharmacien extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string | null

  @column({ columnName: 'address' })
  public address: string | null;

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number | null

  @column({ columnName: 'quarter_id' })
  public quarterId: number | null

  @column({ columnName: 'gender' })
  public gender: string

  @column({ columnName: 'profession' })
  public profession: string | null

  @column({ columnName: 'birthday_year' })
  public birthdayYear: number

  @column({ columnName: 'birthday_month' })
  public birthdayMonth: number

  @column({ columnName: 'birthday_day' })
  public birthdayDay: number

  @column({ columnName: 'domain_id' })
  public domainId: number | null

  @column({ columnName: 'docs' })
  public docs: any

  @column({ columnName: 'pharmacy_id' })
  public pharmacyId: number

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'code' })
  public code: string

  @column({columnName: 'role'})
  public role: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Pharmacy, {
    foreignKey: 'pharmacyId',
  })
  public pharmacy: BelongsTo<typeof Pharmacy>

  @belongsTo(() => User, {
    foreignKey: 'userId',
    localKey: 'id'
  })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Country, {
    foreignKey: 'countryId',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => City, {
    foreignKey: 'cityId',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City>

  @belongsTo(() => Quarter, {
    foreignKey: 'quarterId',
    localKey: 'id'
  })
  public quarter: BelongsTo<typeof Quarter>
}
