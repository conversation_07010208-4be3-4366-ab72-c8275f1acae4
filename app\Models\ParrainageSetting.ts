import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class ParrainageSetting extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'libelle_plan'})
  public libellePlan: string

  @column({columnName: 'profile'})
  public profile: string | null

  @column({columnName: 'metadata'})
  public metadata: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
