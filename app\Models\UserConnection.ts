import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class UserConnection extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'ip_address' })
  public ipAddress: string

  @column({ columnName: 'user_agent' })
  public userAgent: string

  @column({ columnName: 'device_type' })
  public deviceType: string

  @column({ columnName: 'type' })
  public type: string

  @column.dateTime({ columnName: 'connected_at' })
  public connectedAt: DateTime

  @column.dateTime({ columnName: 'disconnected_at' })
  public disconnectedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
