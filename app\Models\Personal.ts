import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Soignant from './Soignant'
import HealthInstitute from './HealthInstitute'

export default class Personal extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @column({ columnName: 'soignant_id' })
  public soignantId: number

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column({ columnName: 'is_principal' })
  public isPrincipal: boolean

  @column({ columnName: 'role' })
  public role: string

  @column({ columnName: 'departement' })
  public departement: string

  @column({ columnName: 'matricule' })
  public matricule: string

  @column({ columnName: 'note' })
  public note: string

  @column({ columnName: 'affiliation_date' })
  public affiliationDate: Date

  @column({ columnName: 'end_date' })
  public endDate: Date

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Soignant, {
    foreignKey: 'soignantId',
    localKey: 'id',
  })
  public soignant: BelongsTo<typeof Soignant>

  @belongsTo(() => HealthInstitute, {
    foreignKey: 'healthInstituteId',
    localKey: 'id',
  })
  public healthInstitute: BelongsTo<typeof HealthInstitute>
}
