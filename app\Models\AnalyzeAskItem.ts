import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import AnalyzeAsk from './AnalyzeAsk'
import Analyze from './Analyze'

export default class AnalyzeAskItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number

  @column({ columnName: 'analyze_id' })
  public analyzeId: number

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number

  @column({ columnName: 'price' })
  public price: number

  @column({ columnName: 'can_be_ordered' })
  public canBeOrdered: boolean

  @column({ columnName: 'used_insurance' })
  public usedInsurance: boolean

  @column({ columnName: 'ca_certificat_id' })
  public caCertificatId: number

  @column({ columnName: 'medecin_conseil_id' })
  public medecinConseilId: number

  @column({ columnName: 'justificatif' })
  public justificatif: string

  @column({ columnName: 'is_validated' })
  public isValidated: boolean

  @column({ columnName: 'is_ordered' })
  public isOrdered: boolean

  @column({ columnName: 'result_is_defined' })
  public resultIsDefined: boolean | null

  @column({ columnName: 'quantity' })
  public quantity: number = 1

  @column({ columnName: 'is_paid' })
  public isPaid: boolean

  @column.dateTime({ columnName: 'paid_at' })
  public paidAt: DateTime

  @belongsTo(() => AnalyzeAsk, {
    foreignKey: 'analyzeAskId',
    localKey: 'id',
  })
  public analyzeAsk: BelongsTo<typeof AnalyzeAsk>

  @belongsTo(() => Analyze, {
    foreignKey: 'analyzeId',
    localKey: 'id',
  })
  public analyze: BelongsTo<typeof Analyze>
}
