import Route from '@ioc:Adonis/Core/Route';
import ProfileController from 'App/Controllers/Http/api/auth/ProfileController';

const profilCtrl = new ProfileController();

Route.group(() => {
  Route.group(() => {
    Route.get('reversal-requests', async (ctx) => {
      return await profilCtrl.getReversalRequests(ctx);
    });
    Route.get('/', async (ctx) => {
      return await profilCtrl.getUserParrainage(ctx);
    });
    Route.post('monetization-requests/add', async (ctx) => {
      return await profilCtrl.addMonetizationRequest(ctx);
    });
    Route.post('reversal-requests/add', async (ctx) => {
      return await profilCtrl.addNewReversalRequest(ctx);
    });
  }).prefix('parrainage').middleware(['auth']);

}).prefix('api').namespace('App/Controllers/Http/api/auth');
