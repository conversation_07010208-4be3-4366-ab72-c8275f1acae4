import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class MedicalFacilitator extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  // Skip existing code...

  @column()
  public user_id: number

  @column()
  public last_name: string

  @column()
  public first_name: string

  @column()
  public phone: string

  @column()
  public email: string

  @column()
  public address: string

  @column()
  public country_id: number

  @column()
  public city_id: number

  @column()
  public quarter_id: number

  @column()
  public gender: string

  @column()
  public birthday_year: number

  @column()
  public birthday_month: number

  @column()
  public birthday_day: number

  @column()
  public profession: string
  // Skip existing code...

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
