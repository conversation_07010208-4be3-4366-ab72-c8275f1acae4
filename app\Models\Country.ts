import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import City from './City';

export default class Country extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'name'})
  public name: string;

  @column({columnName: 'iso'})
  public iso: string;

  @column({columnName: 'prefix'})
  public prefix: string;

  @column({columnName: 'flag'})
  public flag: string;

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => City)
  public cities: HasMany<typeof City>
}
