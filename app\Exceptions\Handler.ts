/*
|--------------------------------------------------------------------------
| Http Exception Handler
|--------------------------------------------------------------------------
|
| AdonisJs will forward all exceptions occurred during an HTTP request to
| the following class. You can learn more about exception handling by
| reading docs.
|
| The exception handler extends a base `HttpExceptionHandler` which is not
| mandatory, however it can do lot of heavy lifting to handle the errors
| properly.
|
*/

import Logger from '@ioc:Adonis/Core/Logger'
import HttpExceptionHandler from '@ioc:Adonis/Core/HttpExceptionHandler'
import { ApiResponse } from 'App/Controllers/Utils/models';

export default class ExceptionH<PERSON>ler extends HttpExceptionHandler {
  constructor () {
    super(Logger)
  }

  public async handle(error: any, ctx: any) {
    let apiResponse: ApiResponse = {
      success: false,
      message: error.message,
      result: null
    };
    let status = 422;

    if (error.code === 'E_VALIDATION_FAILURE') {
      apiResponse = {
        success: false,
        message: "Erreur de validation des données",
        result: null,
        except: error.message.split('.')[1],
        errors: error.message.errors
      };
      status = 422;
      return ctx.response.status(status).send(apiResponse);
    }

    if (error.code === 'E_ROUTE_NOT_FOUND') {
      status = 404;
      apiResponse = {
        success: false,
        message: "Route introuvable ou la methode est incorrecte",
        result: null,
        except: error.message,
        errors: error.message.errors
      };
      return ctx.response.status(404).send(apiResponse);
    }

    if (error.code === 'E_UNAUTHORIZED_ACCESS') {
      status = 401;
      apiResponse = {
        success: false,
        message: "Accès interdit",
        result: null,
        except: error.message,
        errors: error.message.errors
      };
      return ctx.response.status(401).send(apiResponse);
    }

    if (error.code == "ER_NO_SUCH_TABLE") {
      // console.log("error handle",error); 
      status = 404;
      apiResponse = {
        success: false,
        message: "Table introuvable",
        result: null,
        except: error.message,
        errors: error.message.errors
      };
      return ctx.response.status(404).send(apiResponse);
    }

    if (error.code == "ER_NO_DEFAULT_FOR_FIELD") {
      status = 400;
      apiResponse = {
        success: false,
        message: "Une erreur sql s'est produite",
        result: null,
        except: error.sqlMessage
      }
    }

    if (error.code == "ER_BAD_FIELD_ERROR") {

      status = 400;
      apiResponse = {
        success: false,
        message: "Une erreur sql s'est produite",
        result: null,
        except: error.sqlMessage
      }
    }

    return super.handle(apiResponse, ctx);
  }
}
