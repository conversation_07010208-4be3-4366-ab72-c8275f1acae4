import Hash from '@ioc:Adonis/Core/Hash';
import { schema, rules } from '@ioc:Adonis/Core/Validator';
import Database from '@ioc:Adonis/Lucid/Database';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HelperController from "../../helpers/HelperController";
import { ApiResponse, HealthRecord, UserStatus } from 'App/Controllers/Utils/models';
import { formatErrorResponse } from 'App/Controllers/Utils';
import Soignant from 'App/Models/Soignant';
import Patient from 'App/Models/Patient';
import User from 'App/Models/User';
import Wallet from 'App/Models/Wallet';
import Card from 'App/Models/Card';
import { DateTime } from 'luxon';
import Code from 'App/Models/Code';
import HealthBook from 'App/Models/HealthBook';
import VitalParameter from 'App/Models/VitalParameter';
import Diagnostic from 'App/Models/Diagnostic';
import PatientInsuranceCompany from 'App/Models/PatientInsuranceCompany';
import PackageProduct from 'App/Models/PackageProduct';
import PackageAnalyze from 'App/Models/PackageAnalyze';

export default class PatientController extends HelperController {

  public async getPatientList({ auth, response, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 50);
      const authUser = await auth.authenticate();
      if (authUser) {

        const entity = await Soignant.query().where('user_id', authUser.id).first();

        if (entity) {
          let data = {
            patients: [] as Patient[],
            users: [] as Patient[],
          }

          let patients_create = await Patient.query().where('creator_id', entity.id).where('creator_type', 'soignant')
            .preload('healthInstitute').preload('country').preload('city').preload('quarter').preload('carnet', async (query) => {
              query.preload('card');
            })
            .preload('bloodGroup').preload('user').orderBy('created_at', 'desc')
            .paginate(page, limit);

          let health_books = await HealthBook.query().where('soignant_id', entity.id).where('status', 1).preload('patient', async (query) => {
            query.preload('user').preload('country').preload('city').preload('quarter').preload('carnet', async (query) => {
              query.preload('card');
            }).preload('bloodGroup').preload('healthInstitute');
          }).orderBy('created_at', 'desc').paginate(page, limit);


          patients_create.forEach((patient) => {
            if (patient.carnet_is_active && patient.carnet !== null) {
              data.patients.push(patient);
            } else {
              data.users.push(patient);
            }
          });

          health_books.forEach((book) => {
            let patient = book.patient;
            if (patient.carnet_is_active && patient.carnet !== null) {
              if (!data.patients.some(p => p.id === patient.id)) {
                data.patients.push(patient);
              }
            }
          });

          const jsonData = health_books.toJSON();
          const jsonData2 = patients_create.toJSON();

          apiResponse = {
            success: true,
            message: "Patient list",
            result: {
              data: data,
              meta: jsonData.meta,
              meta_user: jsonData2.meta,
            },
          }
        } else {
          apiResponse = {
            success: false,
            message: "User not found",
            result: null as any,
            except: null as any
          }
        }
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getPatientActifs({ auth, response, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 50);
      const authUser = await auth.authenticate();
      if (authUser) {

        const entity = await Soignant.query().where('user_id', authUser.id).first();

        if (entity) {
          let data = {
            patients: [] as Patient[],
            users: [] as Patient[],
          }

          let patients_create = await Patient.query().where('creator_id', entity.id).where('creator_type', 'soignant')
            .preload('healthInstitute').preload('country').preload('city').preload('quarter').preload('carnet', async (query) => {
              query.preload('card');
            })
            .preload('bloodGroup').preload('user').orderBy('created_at', 'desc')
            .paginate(page, limit);

          let health_books = await HealthBook.query().where('soignant_id', entity.id).where('status', 1).preload('patient', async (query) => {
            query.preload('user').preload('country').preload('city').preload('quarter').preload('carnet', async (query) => {
              query.preload('card');
            }).preload('bloodGroup').preload('healthInstitute');
          }).orderBy('created_at', 'desc').paginate(page, limit);


          patients_create.forEach((patient) => {
            if (patient.carnet_is_active && patient.carnet !== null) {
              data.patients.push(patient);
            } else {
              data.users.push(patient);
            }
          });

          health_books.forEach((book) => {
            let patient = book.patient;
            if (patient.carnet_is_active && patient.carnet !== null) {
              if (!data.patients.some(p => p.id === patient.id)) {
                data.patients.push(patient);
              }
            }
          });

          const jsonData = health_books.toJSON();

          apiResponse = {
            success: true,
            message: "Patient list",
            result: {
              data: data.patients,
              meta: jsonData.meta,
            },
          }
        } else {
          apiResponse = {
            success: false,
            message: "User not found",
            result: null as any,
            except: null as any
          }
        }
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async createPatientAccount({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    let status = 201;
    let trx = await Database.transaction();
    try {
      const payload = await request.validate({
        schema: schema.create({
          last_name: schema.string({ trim: true }),
          first_name: schema.string({ trim: true }),
          email: schema.string.optional({ trim: true }, [
            rules.email()
          ]),
          phone: schema.string({ trim: true }),
          gender: schema.enum.optional(['M', 'F']),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          blood_group_id: schema.number.optional(),
          is_rural: schema.boolean.optional(),
          is_isme: schema.boolean.optional(),
          country_id: schema.number(),
        }),
        messages: {
          'last_name.required': "Le nom d'utilisateur est requis",
          'email.required': "L'email est requis",
          'phone.required': "Le numero de numéro est requis",
          'password.required': "Veuillez saisir votre mot de passe",
          'gender.required': "Veuillez selectionner votre genre",
          'country_id.required': "Veuillez selectionner votre pays",
          'blood_group_id.required': "Veuillez selectionner votre groupe sanguin",
          'birthday_year.required': "Veuillez selectionner votre annee de naissance",
          'birthday_month.required': "Veuillez selectionner votre mois de naissance",
          'birthday_day.required': "Veuillez selectionner votre jour de naissance",
        }
      });
      const { last_name, first_name, email, phone, gender, birthday_year, birthday_month,
        birthday_day, blood_group_id, is_rural, is_isme, country_id
      } = payload;
      const authUser = await auth.authenticate();
      if (authUser) {
        const creator = await User.query().where('id', authUser.id).preload('role').first();
        const entity = await Soignant.query().where('user_id', authUser.id).first();
        let username = `${last_name} ${first_name}`;
        const check_exist_user = await User.query().where('phone', phone).orWhere('email', String(email)).first();
        if (check_exist_user) {
          apiResponse = {
            success: false,
            message: "Ce utilisateur existe déjà",
            result: null as any,
            except: null as any
          }
          status = 400;
          await trx.rollback();
          return response.status(status).json(apiResponse);
        }
        const roleId = await this.getPatientRoleId();
        let codeP = await this.generateCodeParrainage(8);

        const codeParrainage = {
          create_account: 0,
          active_qrcode: 0,
          adhesion_fees: 0,
          plan: 1,
          activeMoney: false
        }
        let password = await Hash.make(phone);
        const user = await User.create({
          username: username,
          phone: phone,
          email: email,
          password: password,
          countryId: country_id,
          roleId: roleId,
          status: UserStatus.Actived,
          codeParrainage: codeP,
          parrainage: JSON.stringify(codeParrainage),
          creatorId: authUser.id,
        }, { client: trx });
        if (user !== null && creator !== null) {
          let codePatient = await this.generateToken();
          const patient = await Patient.create({
            user_id: user.id,
            last_name: last_name,
            first_name: first_name,
            email: email,
            phone: phone,
            gender: gender,
            birthday_year: birthday_year,
            birthday_month: birthday_month,
            birthday_day: birthday_day,
            blood_group_id: blood_group_id,
            isRural: is_rural !== undefined ? is_rural : false,
            is_isme: is_isme !== undefined ? is_isme : false,
            country_id: country_id,
            status: 'activated',
            creator_id: Number(entity?.id),
            creator_type: creator.role.name.toLowerCase(),
            code: codePatient.toString(),
          }, { client: trx });

          if (patient && patient.id) {
            let walletCode = await this.generateWalletCode();
            const wallet = await Wallet.create({
              userId: user.id,
              ownerType: 'patient',
              ownerId: patient.id,
              libelle: "DO WALLET",
              typeWalletId: 2,
              code: walletCode,
            }, { client: trx });
            if (wallet !== null) {
              await trx.commit();

              let message = "DokitaEyes\n\n Votre compte patient DokitaEyes a été créé avec succès. \n" +
                "Voici les informations de connexion : \n\n Nom d'utilisateur : \n" + user.phone + "\n\n" +
                "Mot de passe : \n" + user.password;

              let sendSMS = await this.sendSMS(user.phone, message);
              let sms_is_send = sendSMS.success ? true : false;
              console.log("sendSMS", sendSMS);

              apiResponse = {
                success: true,
                message: "Patient enrégistré avec succès, les informations de connexion ont été envoyées au patient.",
                result: {
                  user: user,
                  codeParrainage: codeParrainage,
                  patient: patient,
                  wallet: wallet,
                  sms: sms_is_send
                },
              }
            } else {
              await trx.rollback();
              apiResponse = {
                success: false,
                message: "Une erreur est survenue lors de la creation de votre compte patient",
                result: null as any,
                except: wallet
              }
              status = 500;
            }
          } else {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Une erreur est survenue lors de la creation de votre compte patient",
              result: null as any,
              except: patient
            }
            status = 500;
          }
        } else {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Une erreur est survenue lors de la creation du compte utilisateur du patient",
            result: null as any,
            except: user
          }
          status = 500;
        }
      }
    } catch (error) {
      console.log("error", error);
      await trx.rollback();
      status = 500;
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async scanPatientQrcode({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    let status = 201;

    try {
      const payload = await request.validate({
        schema: schema.create({
          card_uuid: schema.string({ trim: true }),
          patient_id: schema.number()
        }),
        messages: {
          'code.required': "Veuillez scanner le code QR",
        }
      });

      const { card_uuid } = payload;
      const authUser = await auth.authenticate();

      if (!authUser) {
        return response.status(401).json({
          success: false,
          message: "Vous n'êtes pas autorisé à accéder à cette fonctionnalité",
        });
      }

      const entity = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id);
      if (!entity) {
        return response.status(404).json({
          success: false,
          message: "Vous n'avez pas accès à cette fonctionnalité",
        });
      }

      const patient = await Patient.query().where('id', payload.patient_id).first();
      if (!patient) {
        return response.status(404).json({
          success: false,
          message: "Patient introuvable",
          result: null,
        });
      }

      if (patient.carnet_is_active && patient.carnet !== null) {
        return response.status(400).json({
          success: false,
          message: "Ce patient a déjà un carnet",
          result: patient,
        });
      }

      // Verrouiller la carte sélectionnée pour éviter des conflits (FOR UPDATE)
      let card = await Card.query()
        .where('uid', card_uuid)
        .where('is_paid', false)
        .andWhere('type', 'physical')
        .andWhere('is_used', false)
        .andWhere('status', 'new')
        .forUpdate() // Verrouiller la carte pendant la transaction
        .first();

      if (!card) {
        return response.status(400).json({
          success: false,
          message: "Echec, aucune carte ne correspond à votre code, veuillez réessayez avec une autre carte svp ..!",
        });
      }

      // Démarrer la transaction seulement ici
      const trx = await Database.transaction();

      try {
        const validity = 365;
        const newCode = await Code.create({
          patientId: patient.id,
          cardId: card.id,
          validity: validity.toString(),
          activatedAt: DateTime.now().toISODate(),
          expiredAt: DateTime.now().plus({ days: validity }).toISODate(),
          online_paid: false,
        }, { client: trx });

        if (!newCode) {
          throw new Error("Erreur lors de la création du code");
        }

        // Mettre à jour la carte
        await card
          .useTransaction(trx)
          .merge({
            isUsed: true,
            isPaid: true,
            status: 'used',
          }).save();

        // Mettre à jour le patient
        await patient
          .useTransaction(trx)
          .merge({
            carnet_is_active: true,
          }).save();

        // Créer le carnet de santé
        const healthBook = await HealthBook.create({
          patientId: patient.id,
          proId: entity.id,
          status: true,
        }, { client: trx });

        if (!healthBook) {
          throw new Error("Erreur lors de la création du carnet de santé");
        }

        await trx.commit();

        apiResponse = {
          success: true,
          message: "Paiement effectué avec succès, votre carnet de santé a été activé",
          result: {
            patient: patient,
            card: card,
            code: newCode,
            healthBook: healthBook,
          },
        };

      } catch (error) {
        await trx.rollback();
        throw error;
      }

    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error.message,
      };
    }
    return response.status(status).json(apiResponse);
  }


  public async addPatientToHealthBook({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 201;
    const trx = await Database.transaction();

    try {
      // Validation du payload
      const payload = await request.validate({
        schema: schema.create({
          card_uuid: schema.string({ trim: true }),
        }),
        messages: {
          'patient_id.required': "Veuillez scanner le code QR",
          'card_uuid.required': "Veuillez scanner le code QR",
        }
      });

      // Authentification de l'utilisateur
      const authUser = await auth.authenticate();
      if (!authUser) {
        return response.status(401).json({ ...apiResponse, message: "Vous n'êtes pas autorisé à accéder à cette fonctionnalité" });
      }

      // Récupération de l'entité liée à l'utilisateur
      const entity = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id);
      if (!entity) {
        return response.status(404).json({ ...apiResponse, message: "Vous n'avez pas accès à cette fonctionnalité" });
      }

      // Récupération de la carte
      const card = await Card.query()
        .where('uid', payload.card_uuid)

        .andWhere('is_active', true)
        .andWhereNot('status', 'blocked')
        .first();

      if (!card) {
        return response.status(404).json({ ...apiResponse, message: "Aucune carte ne correspond à votre code" });
      }
      //get code by card
      const res_code = await Code.query().where('card_id', card.id).preload('patient').first();
      if (!res_code || !res_code.patient) {
        return response.status(404).json({ ...apiResponse, message: "Aucun code ne correspond à votre carte" });
      }
      // Récupération du patient
      const patient = await Patient.query().where('id', res_code.patientId).first();
      if (!patient) {
        return response.status(404).json({ ...apiResponse, message: "Ce patient n'existe pas" });
      }

      // Vérification si le carnet est déjà actif
      if (patient.carnet_is_active === false || patient.carnet === null) {
        return response.status(400).json({ ...apiResponse, message: "Ce patient n'a pas de carnet actif" });
      }

      // Vérification de la validité du code
      const today = DateTime.now().toISODate();
      const code = await Code.query()
        .where('card_id', card.id)
        .andWhere('patient_id', patient.id)
        .whereNull('blocked_at')
        .where('expired_at', '>=', today)
        .first();

      if (!code) {
        const expiredCode = await Code.query().where('card_id', card.id).andWhere('patient_id', patient.id).first();
        let errorMsg = "Echec, une erreur est survenue lors de la validation du compte patient";

        if (expiredCode) {
          if (expiredCode.expiredAt && expiredCode.expiredAt < today) {
            errorMsg = "Echec, la carte du patient a expiré";
          } else if (expiredCode.blockedAt !== null) {
            errorMsg = "Echec, la carte du patient a été bloquée";
          }
        }
        return response.status(400).json({ ...apiResponse, message: errorMsg });
      }

      // Vérification si le patient est déjà dans le portefeuille de l'entité
      const checkExist = await HealthBook.query().where('patient_id', patient.id).andWhere('soignant_id', entity.id).first();
      if (checkExist && checkExist !== null) {
        if (checkExist.status === false) {
          await checkExist.useTransaction(trx).merge({
            status: true,
          }).save();
          await trx.commit();
          return response.status(200).json({
            success: true,
            message: "Le patient a été ajouté à votre portefeuille",
            result: patient,
          });
        }
      }

      // Création du carnet de santé
      const healthBook = await HealthBook.create({
        patientId: patient.id,
        proId: entity.id,
        status: true,
      }, { client: trx });

      // Confirmation de la transaction
      await trx.commit();

      apiResponse = {
        success: true,
        message: "Patient ajouté à votre compte avec succès",
        result: {
          patient: patient,
          card: card,
          code: code,
          healthBook: healthBook,
        },
      };
    } catch (error) {
      console.log("error in add patient", error.message);
      await trx.rollback();
      status = 500;
      apiResponse = {
        ...apiResponse,
        message: "Une erreur est survenue lors du traitement",
        except: error.message,
      };
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientDetails({ auth, response, request }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number.optional(),
          code: schema.string.optional(),
        }),
        messages: {
          'patient_id.required': "Veuillez envoyer le patient_id",
        }
      });
      const { patient_id, code } = payload;
      const authUser = await auth.authenticate();
      if (authUser) {
        let query = Patient.query();
        if (patient_id) {
          query = query.where('id', patient_id);
        }
        if (code) {
          query = query.where('code', code);
        }
        const patient = await query
          .preload('healthInstitute')
          .preload('country')
          .preload('city')
          .preload('quarter')
          .preload('carnet', function (query) {
            query.preload('card');
          })
          .preload('bloodGroup')
          .preload('user')
          .first();
        if (patient == null) {
          apiResponse = {
            success: false,
            message: "Patient non trouvé",
            result: patient,
          }
          return response.status(404).json(apiResponse);
        }

        apiResponse = {
          success: true,
          message: "Patient trouvé",
          result: patient,
        }
      }
    } catch (error) {
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getPatientInsurances({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    };
    let status = 200;
    try {
      const patientId = request.input('patient_id');
      if (!patientId) {
        apiResponse.message = "Veuillez envoyer le patient_id";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const patient = await Patient.query().where('id', patientId).first();
      if (!patient) {
        apiResponse.message = "Patient non trouvé";
        return response.status(404).json(apiResponse);
      }
      const insurances = await PatientInsuranceCompany.query().where('patient_id', patient.id)
        .preload('insurance_company')
        .preload('active_subscription', function (query) {
          query.preload('package');
        })
        .orderBy('created_at', 'desc');

      apiResponse = {
        success: true,
        message: 'Patient insurances retrieved successfully',
        result: {
          insurances,
          patient,
        },
      };
    } catch (error) {
      console.log("error", error);
      status = 500;
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error.message,
      };
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientDiagnostics({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
      except: null,
    };
    let status = 200;
    try {
      const patientId = request.input('patient_id');
      const page = request.input('page', 1);
      const limit = request.input('limit', 50);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse = {
          success: false,
          message: 'You are not authorized to access this feature',
          result: null,
        };
        return response.status(401).json(apiResponse);
      }
      const patient = await Patient.query().where('id', patientId).first();
      if (!patient) {
        apiResponse = {
          success: false,
          message: 'Patient not found',
          result: null,
        };
        return response.status(404).json(apiResponse);
      }
      const diagnostic = await Diagnostic.query().where('patient_id', patient.id).orderBy('created_at', 'desc')
        .preload('pathology')
        .preload('category')
        .preload('pro')
        .preload('healthInstitute')
        .preload('prescriptions', function (query) {
          query.preload('items', function (q) {
            q.preload('product').preload('substitutableProduct')
          })
        })
        .preload('analyze_asks', function (query) {
          query.preload('items', function (q) {
            q.preload('analyze')
          })
        })
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: 'Diagnostic retrieved successfully',
        result: diagnostic,
        except: null,
      };
    } catch (error) {
      console.log("error", error);
      status = 500;
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error.message,
      };
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientVitalParams({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
      except: null,
    };
    let status = 200;
    try {
      let patientId = request.input('patient_id');
      let page = request.input('page', 1);
      let limit = request.input('limit', 50);
      const authUser = await auth.authenticate();
      if (!authUser) {
        return response.status(401).json({
          success: false,
          message: 'You are not authorized to access this feature',
        });
      }
      const patient = await Patient.query().where('id', patientId).first();
      if (!patient) {
        apiResponse = {
          success: false,
          message: 'Patient not found',
          result: null,
        };
        return response.status(404).json(apiResponse);
      }
      const vitalParams = await VitalParameter.query().where('patient_id', patient.id).orderBy('created_at', 'desc')
        .preload('diagnostic').preload('creator').preload('vitalExam')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: 'Vital Params retrieved successfully',
        result: vitalParams,
        except: null,
      };
    } catch (error) {
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error.message,
      };
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientVitalParamsWithFormat({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null,
    };
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 50);
      const patientId = request.input('patient_id');

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(200).json(apiResponse);
      }

      const patient = await Patient.query().where('id', patientId).first();
      if (!patient) {
        apiResponse = {
          success: false,
          message: 'Patient not found',
          result: null,
        };
        return response.status(404).json(apiResponse);
      }

      // Récupérer les paramètres vitaux avec pagination
      const vitalParams = await VitalParameter.query()
        .where('patient_id', patient.id)
        .orderBy('created_at', 'desc')
        .preload('vitalExam')
        .preload('diagnostic')
        .preload('creator')
        .paginate(page, limit);


      // Transformer les paramètres vitaux en format JSON
      const paramsData = vitalParams.toJSON();

      // Regrouper les paramètres par date de création (sans l'heure)
      const groupedParams = paramsData.data.reduce((acc, param) => {
        // Format de la date en 'jj mois aaaa'
        const formattedDate = new Date(param.createdAt).toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        // Si la date n'existe pas encore dans l'accumulateur, l'ajouter
        if (!acc[formattedDate]) {
          acc[formattedDate] = [];
        }
        acc[formattedDate].push(param);
        return acc;
      }, {} as { [key: string]: any[] });

      // Convertir l'objet regroupé en tableau de dates
      const result = Object.keys(groupedParams).map((date) => ({
        date: date,
        params: groupedParams[date]
      }));

      apiResponse = {
        success: true,
        message: 'Liste des paramètres vitaux par date',
        result: {
          meta: paramsData.meta,
          data: result
        }
      };
    } catch (error) {
      console.log(error);
      apiResponse = {
        success: false,
        message: `Erreur de récupération des paramètres vitaux : ${error.message}`,
        except: error.message,
        result: null
      };
    }
    return response.status(200).json(apiResponse);
  }

  public async getPatientvitalParamsByExam({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null,
    };
    let status = 200;
    try {
      const authUser = await auth.authenticate();
      const vitalExamId = request.input('vital_exam_id');
      const patientId = request.input('patient_id');
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const patient = await Patient.query().where('id', patientId).first();
      if (!patient) {
        apiResponse = {
          success: false,
          message: 'Patient not found',
          result: null,
        };
        return response.status(404).json(apiResponse);
      }

      const vitalParams = await VitalParameter.query().where('patient_id', patient.id).where('vital_exam_id', vitalExamId).orderBy('created_at', 'asc')
        .preload('creator').preload('vitalExam');

      apiResponse = {
        success: true,
        message: "Les paramètres vitaux ont été récupérés",
        result: vitalParams,
      }

    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Erreur de récupérations des paramètres vitaux",
        result: null,
        except: error.message
      };
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async addPatientVitalParameter({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
      except: null,
    };
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number(),
          vital_exam_id: schema.number(),
          libelle: schema.string(),
          value: schema.string.optional(),
          values: schema.object.optional().anyMembers(),
          multiple_readings: schema.boolean(),
          unit: schema.string.optional(),
          diagnostic_id: schema.number.optional(),
          visite_id: schema.number.optional(),
        })
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        return response.status(401).json({
          success: false,
          message: "Vous n'êtes pas autorisé à accéder à cette fonctionnalité",
        });
      }

      const vitalParameter = await VitalParameter.create({
        ...payload,
        creatorId: authUser.id,
        creatorType: 'soignant',
      });

      apiResponse = {
        success: true,
        message: "Paramètre vital ajouté avec succès",
        result: vitalParameter,
      };
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Erreur lors de l'ajout du paramètre vital",
        result: null,
        except: error.message
      };
      return response.status(status).json(apiResponse);
    }
  }

  public async addPatientHealthRecord({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
      except: null,
    };
    let status = 201;

    try {
      // Validation de la requête
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number(),
          type: schema.enum(['allergy', 'handicap', 'antecedent']),
          items: schema.array().members(
            schema.object().members({
              libelle: schema.string(),
              description: schema.string.optional(),
            })
          ),
        }),
      });

      const { patient_id, type, items } = payload;
      const authUser = await auth.authenticate();

      // Vérification de l'authentification
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      // Récupération de l'entité soignant liée à l'utilisateur
      const pro = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id) as Soignant;
      if (!pro) {
        return response.status(404).json({ ...apiResponse, message: "Vous n'avez pas accès à cette fonctionnalité" });
      }

      // Récupération du patient
      const patient = await Patient.query().where('id', patient_id).first();
      if (!patient) {
        return response.status(404).json({ ...apiResponse, message: "Ce patient n'existe pas" });
      }

      // Initialisation des propriétés à un tableau vide si elles sont nulles
      patient.allergies = patient.allergies ?? [];
      patient.handicapes = patient.handicapes ?? [];
      patient.antecedents = patient.antecedents ?? [];


      // Extraction et parsing des enregistrements existants
      let records: HealthRecord[] = [];
      switch (type) {
        case 'allergy':
          records = patient.allergies;
          break;
        case 'handicap':
          records = patient.handicapes;
          break;
        case 'antecedent':
          records = patient.antecedents;
          break;
      }

      // Debugging
      // console.log("Patient Record:", patient);
      console.log("Existing Records for type:", type, "=>", records);

      // Génération d'ID unique pour tous les nouveaux éléments
      const generateUniqueId = (existingRecords: HealthRecord[]): number => {
        const maxId = existingRecords.length > 0
          ? Math.max(...existingRecords.map((record) => record.id ?? 0))
          : 0;
        return maxId + 1;
      };

      // Obtenez le dernier ID pour le patient
      const lastId = generateUniqueId(records);

      // Créez les nouveaux enregistrements avec des IDs uniques
      const newRecords = items.map((item, index) => ({
        id: lastId + index, // Attribution d'un nouvel ID unique
        libelle: item.libelle,
        description: item.description,
        creator: `${pro.lastName} ${pro.firstName}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));


      // Mise à jour des enregistrements du patient
      records = [...records, ...newRecords];

      // Mise à jour des enregistrements dans la base de données
      switch (type) {
        case 'allergy':
          patient.allergies = records;
          break;
        case 'handicap':
          patient.handicapes = records;
          break;
        case 'antecedent':
          patient.antecedents = records;
          break;
      }

      await patient.save();
      apiResponse.success = true;
      apiResponse.message = 'Enregistrement ajouté avec succès';
      apiResponse.result = newRecords;

      return response.status(status).json(apiResponse);

    } catch (error) {
      status = 500;
      console.log("error", error);

      apiResponse.message = formatErrorResponse(error);
      apiResponse.except = error.message;
      return response.status(status).json(apiResponse);
    }
  }

  public async deletePatientHealthRecord({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    };
    let status = 200;

    try {
      // Validation de la requête
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number(),
          type: schema.enum(['allergy', 'handicap', 'antecedent']),
          record_id: schema.number(), // ID de l'élément à supprimer
        }),
      });

      const { patient_id, type, record_id } = payload;
      const authUser = await auth.authenticate();

      // Vérification de l'authentification
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      // Récupération de l'entité soignant liée à l'utilisateur
      const pro = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id) as Soignant;
      if (!pro) {
        return response.status(404).json({ ...apiResponse, message: "Vous n'avez pas accès à cette fonctionnalité" });
      }

      // Récupération du patient
      const patient = await Patient.query().where('id', patient_id).first();
      if (!patient) {
        return response.status(404).json({ ...apiResponse, message: "Ce patient n'existe pas" });
      }

      // Extraction et parsing des enregistrements existants
      let records: HealthRecord[] = [];
      switch (type) {
        case 'allergy':
          records = patient.allergies || [];
          break;
        case 'handicap':
          records = patient.handicapes || [];
          break;
        case 'antecedent':
          records = patient.antecedents || [];
          break;
      }

      // Suppression de l'enregistrement correspondant à l'ID
      const updatedRecords = records.filter(record => record.id !== record_id);

      // Mise à jour des enregistrements du patient
      switch (type) {
        case 'allergy':
          patient.allergies = updatedRecords;
          break;
        case 'handicap':
          patient.handicapes = updatedRecords;
          break;
        case 'antecedent':
          patient.antecedents = updatedRecords;
          break;
      }

      await patient.save(); // Sauvegarde des modifications
      apiResponse.success = true;
      apiResponse.message = 'Enregistrement supprimé avec succès';
      apiResponse.result = updatedRecords;

      return response.status(status).json(apiResponse);

    } catch (error) {
      status = 500;
      apiResponse.message = formatErrorResponse(error);
      apiResponse.except = error.message;
      return response.status(status).json(apiResponse);
    }
  }

  public async updatePatientHealthRecord({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;

    try {
      // Validation de la requête
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number(),
          type: schema.enum(['allergy', 'handicap', 'antecedent']),
          record_id: schema.number(), // ID de l'enregistrement à mettre à jour
          libelle: schema.string.optional(),
          description: schema.string.optional(),
        }),
      });

      const { patient_id, type, record_id, libelle, description } = payload;
      const authUser = await auth.authenticate();

      // Vérification de l'authentification
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      // Récupération de l'entité soignant liée à l'utilisateur
      const pro = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id) as Soignant;
      if (!pro) {
        return response.status(404).json({ ...apiResponse, message: "Vous n'avez pas accès à cette fonctionnalité" });
      }

      // Récupération du patient
      const patient = await Patient.query().where('id', patient_id).first();
      if (!patient) {
        return response.status(404).json({ ...apiResponse, message: "Ce patient n'existe pas" });
      }

      // Extraction des enregistrements existants
      let records: HealthRecord[] = [];
      switch (type) {
        case 'allergy':
          records = patient.allergies || [];
          break;
        case 'handicap':
          records = patient.handicapes || [];
          break;
        case 'antecedent':
          records = patient.antecedents || [];
          break;
      }

      // Trouver et mettre à jour l'enregistrement par ID
      const recordIndex = records.findIndex(record => record.id === record_id);
      if (recordIndex === -1) {
        return response.status(404).json({ ...apiResponse, message: "L'enregistrement spécifié n'existe pas" });
      }

      // Mise à jour de l'enregistrement
      records[recordIndex].libelle = libelle || records[recordIndex].libelle;
      records[recordIndex].description = description || records[recordIndex].description;
      records[recordIndex].updated_at = new Date().toISOString();

      // Mise à jour des enregistrements du patient dans la base de données
      switch (type) {
        case 'allergy':
          patient.allergies = records;
          break;
        case 'handicap':
          patient.handicapes = records;
          break;
        case 'antecedent':
          patient.antecedents = records;
          break;
      }

      await patient.save(); // Sauvegarde des modifications
      apiResponse.success = true;
      apiResponse.message = "Enregistrement mis à jour avec succès";
      apiResponse.result = records[recordIndex];

      return response.status(status).json(apiResponse);

    } catch (error) {
      status = 500;
      apiResponse.message = formatErrorResponse(error);
      apiResponse.except = error.message;
      return response.status(status).json(apiResponse);
    }
  }

  public async getInsurancePackage({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 12;
      const packageId = request.input('package_id');
      const type = request.input('type', 'product') as 'product' | 'analyze';

      if (!packageId) {
        apiResponse.message = "Veuillez envoyer le package_id";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      let query;
      let result;
      if (type === 'analyze') {
        query = PackageAnalyze.query()
          .where('package_id', packageId)
          .preload('analyze')

      } else {
        query = PackageProduct.query()
          .where('package_id', packageId)
          .preload('product')
      }
      result = await query.paginate(page, limit);

      apiResponse = {
        success: true,
        message: `Liste des ${type === 'analyze' ? 'analyses' : 'produits'} du package`,
        result: result,
      };


    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }



}
