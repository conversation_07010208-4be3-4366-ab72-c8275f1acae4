
SERVICES HEALTHPRO

1-Authentification & Profil
2-Mes patients
3-Mes consultations
4-<PERSON><PERSON>-vous
5-Blog
6-Annuaire
7-Chat
8- Commons :
Pieces à valider pour les comptes pro

1-CNI
2-<PERSON><PERSON><PERSON><PERSON> ou Attestion
3-Attestion de l'inscription au tableau d'ordre (Medecin) (optionel)
4-<PERSON>te professionnelle (Medecin) (optionel)

consultations privates / publics chez le patient

insert into `insurance_company_prescriptions` (`created_at`, `diagnostic_id`, `insurance_company_id`, `insurance_company_subscription_id`, `is_active`, `notes`, `patient_id`, `patient_insurance_company_id`, `prescription_id`, `rejected_reason`, `status`, `total_amount_assured`, `total_items_assured`, `updated_at`, `validated_by`)
values ('2025-04-14 21:44:43', 15, 20, 21, true, NULL, 30501, 20, 95, NULL, 'pending', 445, 7, '2025-04-14 21:44:43', NULL) - Cannot add or update a child row: a foreign key constraint fails (`dokitaeyes-dev`.`insurance_company_prescriptions`, CONSTRAINT `fk_ins_comp_id` FOREIGN KEY (`insurance_company_id`) REFERENCES `insurance_companies` (`id`) ON DELETE CASCADE)
