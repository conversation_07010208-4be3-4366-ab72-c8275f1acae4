
import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Product from './Product'

export default class PrescriptionItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'prescription_id' })
  public prescriptionId: number

  @column({ columnName: 'product_id' })
  public productId: number

  @column({ columnName: 'quantity' })
  public quantity: number

  @column({ columnName: 'dosage' })
  public dosage: string

  @column({ columnName: 'variant' })
  public variant: string

  @column({ columnName: 'posologie' })
  public posologie: string

  @column({ columnName: 'frequency' })
  public frequency: string

  @column({ columnName: 'duration' })
  public duration: string

  @column({ columnName: 'can_be_ordered' })
  public can_be_ordered: boolean

  @column({ columnName: 'used_insurance' })
  public used_insurance: boolean

  @column({ columnName: 'ca_certificat_id' })
  public ca_certificat_id: number

  @column({ columnName: 'medecin_conseil_id' })
  public medecin_conseil_id: number

  @column({ columnName: 'justificatif' })
  public justificatif: string

  @column({ columnName: 'is_validated' })
  public is_validated: boolean

  @column({ columnName: 'is_ordered' })
  public is_ordered: boolean

  @column({ columnName: 'is_paid' })
  public is_paid: boolean

  @column({ columnName: 'paid_at' })
  public paidAt: DateTime

  @column({ columnName: 'substitutable_product_id' })
  public substitutableProductId: number

  @column({ columnName: 'is_substitutable' })
  public isSubstitutable: boolean

  @column({ columnName: 'is_required' })
  public isRequired: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Product, {
    foreignKey: 'productId',
    localKey: 'id',
  })
  public product: BelongsTo<typeof Product>

  @belongsTo(() => Product, {
    foreignKey: 'substitutableProductId',
    localKey: 'id',
  })
  public substitutableProduct: BelongsTo<typeof Product>
}
