import Logger from '@ioc:Adonis/Core/Logger';
// import Env from '@ioc:Adonis/Core/Env';
// import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import https from 'https';
// import fetch from 'node-fetch';
import Role from "App/Models/Role";

// import env from "env";
import { AnalyzeAskItemStatus, ApiResponse, PrescriptionItemStatus } from 'App/Controllers/Utils/models';
import Soignant from 'App/Models/Soignant';
import Pharmacien from 'App/Models/Pharmacien';
import Laborantin from 'App/Models/Laborantin';
import Patient from 'App/Models/Patient';
import HealthBook from 'App/Models/HealthBook';
import QuotationRequest from 'App/Models/QuotationRequest';
import AnalyzeAskItem from 'App/Models/AnalyzeAskItem';
import Order from 'App/Models/Order';
import OrderItem from 'App/Models/OrderItem';
import PrescriptionItem from 'App/Models/PrescriptionItem';

export default class HelperController {

  public async getPatientRoleId() {
    let roleId = 0;
    const res = await Role.query().where('name', 'PATIENT').first();
    if (res) {
      roleId = res.id;
    }
    return roleId;
  }

  public async generateCodeParrainage(length: number) {
    const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    let codeParrainage = '';
    for (let i = 0; i < length; i++) {
      codeParrainage += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return codeParrainage.toUpperCase();
  }

  public async generateToken() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  public async generateWalletCode() {
    const gens = "**********";
    let length = 12;
    let code = '';
    for (let i = 0; i < length; i++) {
      code += gens.charAt(Math.floor(Math.random() * gens.length));
    }
    return code;
  }

  public checkPhoneValidity(phone: string): boolean {
    const indicatif = phone.substring(0, 3);
    if (['228', '233', '221', '225', '229', '226'].includes(indicatif)) {
      return true;
    } else {
      return false;
    }
  }

  public async sendSMS(phone: string, message: string): Promise<ApiResponse> {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }

    try {
      const apiURL = String(process.env.NGH_SINGLE_SMS);
      let apikey = process.env.ZEDKA_API_KEY;
      let apiSecret = process.env.ZEDKA_API_SECRET;

      // const httpsAgent = new https.Agent({
      //   rejectUnauthorized: false
      // });

      let data = {
        from: "DokitaEyes",
        to: phone,
        text: message,
        reference: 1212,
        api_key: apikey,
        api_secret: apiSecret
      }

      const response = await fetch(apiURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data),
      });

      console.log("SMS RESPONSE",response.status);
      if (response.ok) {

        const result = await response.json() as any;
        if (result.status == 200 && result.status_desc== "Success") {
          apiResponse = {
            success: true,
            message: "Message was sent successfully",
            result: result,
            except: null as any
          }
        }else{
          apiResponse = {
            success: false,
            message: result.status_desc,
            result: null as any,
            except: result
          }
        }
      } else {
        apiResponse = {
          success: false,
          message: "Error",
          result: null as any,
          except: null as any
        }
      }
    } catch (error) {
      Logger.error("SINGLE SMS ERROR : " + (error as Error).message);
      apiResponse = {
        success: false,
        message: (error as Error).message,
        result: null as any,
        except: error
      }
    }

    return apiResponse;
  }

  public async getEntityPersonalByRoleId(roleId: number, userId: number) {
    let entity = {} as Soignant | Pharmacien | Laborantin;
    switch (roleId) {
      case 3:
        entity = await Soignant.query().where('user_id', userId).forUpdate().first() as Soignant;
        break;
      case 4:
        entity = await Pharmacien.query().where('user_id', userId).forUpdate().first() as Pharmacien;
        break;

      case 5:
        entity = await Laborantin.query().where('user_id', userId).forUpdate().first() as Laborantin;
        break;
      default:
        break;
    }

    return entity;
  }

  public async checkPatientIsValid(patient: Patient): Promise<ApiResponse> {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    }
    if (patient.carnet === null) {
      apiResponse.message = "Le patient n'a pas encore de carnet de santé numérique";
      return apiResponse;
    }

    if (!patient.carnet_is_active) {
      apiResponse.message = "Le patient ne dispose pas encore de carnet de santé numérique";
      return apiResponse;
    }
    if (patient.carnet && patient.carnet.blockedAt !== null) {
      apiResponse.message = "Le carnet de santé numérique du patient est bloqué";
      return apiResponse;
    }
    // Correction de la comparaison de la date d'expiration
    if (patient.carnet && new Date(patient.carnet.expiredAt) < new Date()) {
      apiResponse.message = "Le carnet de santé numérique du patient a expiré";
      return apiResponse;
    }

    apiResponse.success = true; // Indiquer que le patient est valide
    return apiResponse;
  }

  public async checkPatientDiagnosticAccess(patient: Patient, soignant: Soignant): Promise<ApiResponse> {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    }
    // Vérification des paramètres
    if (!patient || !soignant) {
      apiResponse.message = "Patient ou soignant manquant";
      return apiResponse;
    }

    const checkAccess = await HealthBook.query().where('patient_id', patient.id).andWhere('soignant_id', soignant.id).andWhere('status', 1).first();
    if (checkAccess === null) {
      apiResponse.message = "Vous n'avez pas accès au carnet de santé numérique de ce patient";
      return apiResponse;
    }
    apiResponse.success = true; // Indiquer que l'accès est valide
    return apiResponse;
  }

  private async getItemStatus<T extends PrescriptionItem | AnalyzeAskItem >(
    items: T[],
    orderItems: OrderItem[],
    itemIdKey: keyof OrderItem, // 'prescriptionItemId' ou 'analyzeAskItemId'
    statusKey: keyof T // 'is_paid' ou 'isPaid'
  ): Promise<Array<{
    id: number;
    quantityPrescribed: number;
    quantityPaid: number;
    quantityRemaining: number;
    paidAt: string | null;
    status: 'paid' | 'pending';
  }>> {
    const result: PrescriptionItemStatus[] | AnalyzeAskItemStatus[] = [];
  
    for (const item of items) {
      const relatedOrderItems = orderItems.filter(orderItem => orderItem[itemIdKey] === item.id);
      let totalQuantityPaid = 0;
  
      for (const orderItem of relatedOrderItems) {
        if (orderItem.status === 'paid') {
          totalQuantityPaid += orderItem.quantity;
        }
      }
      
      const quantityRemaining = item.quantity - totalQuantityPaid;
  
      result.push({
        id: item.id,
        quantityPrescribed: item.quantity,
        quantityPaid: totalQuantityPaid,
        quantityRemaining: quantityRemaining,
        paidAt: item.paidAt ? item.paidAt.toString() : null,
        status: item[statusKey] ? 'paid' : 'pending',
      });
    }
    return result;
  }

  public async getPrescriptionStatus(quotationRequest: QuotationRequest): Promise<Array<PrescriptionItemStatus>> {
    try {
      // 1. Récupérer les éléments de prescription liés à la demande de devis
      const prescriptionItems = await PrescriptionItem.query()
        .where('prescription_id', Number(quotationRequest.prescriptionId))
        .preload('product')
        .preload('substitutableProduct');
  
      // 2. Récupérer tous les orderItems associés à cette quotationRequest
      const orders = await Order.query()
        .whereNull('cancelled_at')
        .where('quotation_request_id', quotationRequest.id)
        .where('prescription_id', Number(quotationRequest.prescriptionId))
        .preload('items')
        .orderBy('created_at', 'desc');
  
      // Extraire tous les orderItems dans une seule liste
      const orderItems: OrderItem[] = orders.flatMap(order => order.items);
  
      // 3. Utiliser la fonction utilitaire pour calculer les statuts
      return await this.getItemStatus(
        prescriptionItems,
        orderItems,
        'prescriptionItemId',
        'is_paid'
      );
    } catch (error) {
      console.error("Error in deducing prescription status:", error.message);
      throw new Error("Une erreur est survenue lors de la déduction des statuts des prescriptions");
    }
  }

  public async getAnalyzeAskStatus(quotationRequest: QuotationRequest): Promise<Array<AnalyzeAskItemStatus>> {
    try {
      // 1. Récupérer les éléments d'analyse liés à la demande de devis
      const analyzeAskItems = await AnalyzeAskItem.query()
        .where('analyze_ask_id', Number(quotationRequest.analyzeAskId))
        .preload('analyze');
  
      // 2. Récupérer tous les orderItems associés à cette quotationRequest
      const orders = await Order.query()
        .whereNull('cancelled_at')
        .where('quotation_request_id', quotationRequest.id)
        .where('analyze_ask_id', Number(quotationRequest.analyzeAskId))
        .preload('items')
        .orderBy('created_at', 'desc');
  
      // Extraire tous les orderItems dans une seule liste
      const orderItems: OrderItem[] = orders.flatMap(order => order.items);
  
      // 3. Utiliser la fonction utilitaire pour calculer les statuts
      return await this.getItemStatus(
        analyzeAskItems,
        orderItems,
        'analyzeAskItemId',
        'isPaid'
      );
    } catch (error) {
      console.error("Error in deducing analyze ask status:", error.message);
      throw new Error("Une erreur est survenue lors de la déduction des statuts des analyses");
    }
  }


}
