import Route from '@ioc:Adonis/Core/Route';
import CommonController from 'App/Controllers/Http/api/helpers/CommonController';

const common = new CommonController();

Route.group(() => {
  Route.get('/countries',async(ctx) => {
    return await common.getCountries(ctx);
  });

  Route.get('/pro-roles',async(ctx) => {
    return await common.getproRoles(ctx);
  });

  Route.get('/cities',async (ctx) => {
    return await common.getCities(ctx);
  });
  Route.get('/cities/country',async (ctx) => {
    return await common.getCitiesByCountry(ctx);
  });
  Route.get('/quarters',async (ctx) => {
    return await common.getQuarters(ctx);
  });
  Route.get('/languages',async (ctx) => {
    return await common.getLanguages(ctx);
  });
  Route.get('/payment-gateways',async (ctx) => {
    return await common.getPaymentGateways(ctx);
  });
  Route.get('/health-institutes',async (ctx) => {
    return await common.getHealthInstitutes(ctx);
  });
  Route.get('/type-health-institutes',async (ctx) => {
    return await common.getTypeHealthInstitutes(ctx);
  });
  Route.get('/blood-groups',async (ctx) => {
    return await common.getBloodGroups(ctx);
  });
  Route.get('/pharmacies',async (ctx) => {
    return await common.getPharmacies(ctx);
  });
  Route.get('/laboratoires',async (ctx) => {
    return await common.getLaboratoires(ctx);
  });
  Route.get('/products/category',async (ctx) => {
    return await common.getCategoryProducts(ctx);
  });
  Route.get('/analyzes/category',async (ctx) => {
    return await common.getCategoryAnalyzes(ctx);
  });
  Route.get('/diagnostics/category',async (ctx) => {
    return await common.getCategoryDiagnostics(ctx);
  });
  Route.get('/products/category/:category_id',async (ctx) => {
    return await common.getProductsByCategory(ctx);
  });

  Route.post('/add-wallet', async(ctx) => {
    return await common.generateWalletToEntity(ctx);
  });
  Route.post('/pharmacies/update-channel', async(ctx) => {
    return await common.generateChannelToEntity(ctx);
  })

}).prefix('api')
