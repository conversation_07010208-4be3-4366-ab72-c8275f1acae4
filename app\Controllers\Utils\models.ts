export interface ApiResponse {
  success: boolean
  message: string
  result: any
  except?: any
  errors?: any
}

export interface PaymentOrder {
  amount: number;
  merchant_reference: string;
  description: string;
  callback_url?: string;
  redirect_url?: string;
  type_notif?: 'sms' | 'email';
  gateway_id: number;
  client: OrderClient;
}

export interface PaymentRequest {
  order_reference: string;
  amount: number;
  state: string;
  date_create: string;
  bill_url: string;
  code: string;
  merchant_reference: string;
  client: OrderClient;
  received_amount: number;
  callback_url: string;
  callback_type: null;
  callback_id: null;
  redirect_url: null;
  currency: string;
  ledger: null;
  payment_status: string;
  status: string;
  message: string;
  qrcode_url: string;
  payments_method: PaymentMethod[];
}

interface PaymentMethod {
  gateway: string;
  method: string;
  action: string;
  description: string;
  reference: string;
  id: number;
}

export interface OrderClient {
  lastname: string;
  firstname: string;
  email?: string;
  phone: string;
  city?: string;
  country?: string;
  address1?: string;
  address2?: string;
}

export interface KYCRequest {
  pieceType: string; // CNI, PERMIS, PASSEPORT
  numPiece: string;
  dateExpire?: string; // optionnel
  type_attestation: string; // Attestation CNI, Attestation diplôme, Attestation carte professionnelle
  attestation_number?: string; // optionnel
  attestation_img?: string; // optionnel
  cni_img?: string; // optionnel
  profesional_doc?: string;
}


export interface HealthRecord {
  id: number;
  libelle: string;
  description?: string;
  creator?: string;
  created_at?: string;
  updated_at?: string;
}

export interface UserParrainage{
  plan: number
  activeMoney: boolean
  active_qrcode: number
  adhesion_fees: number
  create_account: number
}

export interface ParrainageMetadata {
  libelle: string;
  description: string;
  value: number;
  key: string;
  required: boolean;
  unit_price: number;
}

export type MonetizationBalance = {
  solde: number;
  currency: string;
  last_update: string;
}

export enum UserStatus {
  Pending = 'pending',
  Actived = 'actived',
  Blocked = 'blocked',
  Deleted = 'deleted',
  Inactive = 'inactive',
  Archived = 'archived',
  SendIdentity = 'send_identity',
  ValidatedIdentity = 'validated_identity',
  RejectedIdentity = 'rejected_identity',
}

export interface VersionItem {
  item_id: number;
  quantity: number;
  type: 'prescription' | 'analyze';
}

export interface QuotationVersion {
  version_id: number;
  created_at: string;
  status: 'pending' | 'approved' | 'rejected' | 'expired'| 'cancelled' | 'paid' | 'partially_paid' | 'completed';
  items: VersionItem[];
}

export interface QuotationNotification {
  version_id: number;
  notified_at: string;
  responded_at: string | null;
  rejected_at: string | null;
  expired_at: string | null;
  status: 'pending' | 'accepted' | 'rejected' | 'expired' | 'completed';
}

export interface PrescriptionItemStatus {
  id: number;
  quantityPrescribed: number;
  paidAt: string | null;
  quantityPaid: number;
  quantityRemaining: number;
  status: 'pending' | 'paid';
}
export interface AnalyzeAskItemStatus {
  id: number;
  quantityPrescribed: number;
  paidAt: string | null;
  quantityPaid: number;
  quantityRemaining: number;
  status: 'pending' | 'paid';
}

export enum ResultType {
  Numerical = 'numerical',
  Categorical = 'categorical',
  Textual = 'textual',
  Boolean = 'boolean',
  File = 'file',
}
