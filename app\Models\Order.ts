import { DateTime } from 'luxon'
import { <PERSON><PERSON><PERSON><PERSON>, column, <PERSON><PERSON><PERSON>, has<PERSON>any, <PERSON><PERSON><PERSON>, hasOne } from '@ioc:Adonis/Lucid/Orm'
import { belongsTo } from '@ioc:Adonis/Lucid/Orm'
import { BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import QuotationRequest from './QuotationRequest'
import Patient from './Patient'
import Pharmacy from './Pharmacy'
import Laboratory from './Laboratory'
import OrderItem from './OrderItem'
import QuotationProposal from './QuotationProposal'
import Prescription from './Prescription'
import AnalyzeAsk from './AnalyzeAsk'
import Delivery from './Delivery'

export default class Order extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public reference: string

  @column({columnName: 'prescription_id'})
  public prescriptionId: number | null

  @column({columnName: 'analyze_ask_id'})
  public analyzeAskId: number | null

  @column({ columnName: 'quotation_request_id' })
  public quotationRequestId: number

  @column({ columnName: 'quotation_proposal_id' })
  public quotationProposalId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'pharmacy_id' })
  public pharmacyId: number | null

  @column({ columnName: 'laboratory_id' })
  public laboratoryId: number | null

  @column({columnName: 'total_items'})
  public totalItems: number | null

  @column({columnName: 'total_price'})
  public totalPrice: number | null

  @column()
  public totalAssuredPrice: number | null

  @column()
  public status: 'pending_payment' | 'paid' | 'partially_paid' | 'shipped' | 'cancelled' | 'completed'

  @column({ columnName: 'cancel_reason' })
  public reason: string | null

  @column({ columnName: 'date_order' })
  public dateOrder: Date | null

  @column({ columnName: 'paid_at' })
  public paidAt: Date | null

  @column({ columnName: 'shipped_at' })
  public shippedAt: Date | null

  @column({ columnName: 'cancelled_at' })
  public cancelledAt: Date | null

  @column({ columnName: 'completed_at' })
  public completedAt: Date | null

  @belongsTo(() => Prescription, {
    foreignKey: 'prescriptionId',
    localKey: 'id'
  })
  public prescription: BelongsTo<typeof Prescription>

  @belongsTo(() => AnalyzeAsk, {
    foreignKey: 'analyzeAskId',
    localKey: 'id'
  })
  public analyze_ask: BelongsTo<typeof AnalyzeAsk>

  @belongsTo(() => QuotationRequest, {
    foreignKey: 'quotationRequestId',
    localKey: 'id'
  })
  public quotation_request: BelongsTo<typeof QuotationRequest>

  @belongsTo(() => QuotationProposal, {
    foreignKey: 'quotationProposalId',
    localKey: 'id'
  })
  public proposition: BelongsTo<typeof QuotationProposal>

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Pharmacy, {
    foreignKey: 'pharmacyId',
    localKey: 'id'
  })
  public pharmacy: BelongsTo<typeof Pharmacy>

  @belongsTo(() => Laboratory, {
    foreignKey: 'laboratoryId',
    localKey: 'id'
  })
  public laboratory: BelongsTo<typeof Laboratory>

  @hasMany(() => OrderItem, {
    foreignKey: 'orderId',
    localKey: 'id'
  })
  public items: HasMany<typeof OrderItem>

  @hasOne(() => Delivery, {
    foreignKey: 'orderId',
    localKey: 'id',
    onQuery(query) {
      query.orderBy('created_at', 'desc').first();
    },
  })
  public delivery: HasOne<typeof Delivery>
}
