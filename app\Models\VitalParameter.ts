import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import VitalExam from './VitalExam'
import Diagnostic from './Diagnostic'
import Patient from './Patient'
import User from './User'

export default class VitalParameter extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'vital_exam_id'})
  public vitalExamId: number

  @column({columnName: 'patient_id'})
  public patientId: number

  @column({ columnName: 'libelle' })
  public libelle: string | null

  @column({ columnName: 'value' })
  public value: string | null

  @column({ columnName: 'values' })
  public values: Record<string, any> | null;

  @column({ columnName: 'multiple_readings' })
  public multipleReadings: boolean

  @column({ columnName: 'unit' })
  public unit: string | null

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number | null

  @column({ columnName: 'visite_id' })
  public visiteId: number | null

  @column({columnName: 'creator_id'})
  public creatorId: number

  @column({columnName: 'creator_type'})
  public creatorType: 'user' | 'admin' | 'soignant' | 'fmd' | 'asc'

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => VitalExam, {
    foreignKey: 'vitalExamId',
    localKey: 'id'
  })
  public vitalExam: BelongsTo<typeof VitalExam>

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Diagnostic, {
    foreignKey: 'diagnosticId',
    localKey: 'id'
  })
  public diagnostic: BelongsTo<typeof Diagnostic>

  @belongsTo(() => User, {
    foreignKey: 'creatorId',
    localKey: 'id',
  })
  public creator: BelongsTo<typeof User>

  // @belongsTo(() => Visite, {
  //   foreignKey: 'visiteId',
  //   localKey: 'id'
  // })
  // public visite: BelongsTo<typeof Visite>
}
