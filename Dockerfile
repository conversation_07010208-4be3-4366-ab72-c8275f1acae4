ARG NODE_IMAGE=node:18.19.0-alpine

FROM $NODE_IMAGE AS base

WORKDIR /home/<USER>/app

COPY package.json yarn.lock ./
RUN corepack enable && yarn install

COPY . .

# Définir les variables d'environnement pour le projet
ARG PORT
ARG HOST

ENV HOST=0.0.0.0
ENV PORT=$PORT

# Étape de développement
FROM base AS dev
ENV CHOKIDAR_USEPOLLING=true
ENV NODE_ENV=development

# Utilise directement la commande Node.js
CMD ["node", "ace", "serve", "--watch", "--node-args=--inspect=0.0.0.0"]
