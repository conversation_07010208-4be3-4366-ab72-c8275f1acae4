import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import Soignant from './Soignant'

export default class HealthBook extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'patient_id'})
  public patientId: number

  @column({columnName: 'soignant_id'})
  public proId: number

  @column({columnName: 'status'})
  public status: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Soignant, {
    foreignKey: 'proId',
    localKey: 'id'
  })
  public soignant: BelongsTo<typeof Soignant>
}
