import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class InsuranceCompanyHospital extends BaseModel {
  @column({ isPrimary: true })
  public id: number


  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
