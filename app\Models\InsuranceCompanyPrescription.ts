import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import InsuranceCompany from './InsuranceCompany'
import PatientInsuranceCompany from './PatientInsuranceCompany'
import InsuranceCompanySubscription from './InsuranceCompanySubscription'
import Diagnostic from './Diagnostic'
import Prescription from './Prescription'
import InsuranceCompanyPrescriptionItem from './InsuranceCompanyPrescriptionItem'

export default class InsuranceCompanyPrescription extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'patient_insurance_company_id' })
  public patientInsuranceCompanyId: number

  @column({ columnName: 'insurance_company_subscription_id' })
  public insuranceCompanySubscriptionId: number

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number | null

  @column({ columnName: 'prescription_id' })
  public prescriptionId: number

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column({ columnName: 'total_items_assured' })
  public totalItemsAssured: number

  @column({ columnName: 'total_amount_assured' })
  public totalAmountAssured: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'validated' | 'blocked' | 'rejected'

  @column({ columnName: 'validated_by' })
  public validatedBy: number | null

  @column({ columnName: 'notes' })
  public notes: string | null

  @column({ columnName: 'rejected_reason' })
  public rejectedReason: string | null

  @column.dateTime({ columnName: 'validated_at' })
  public validatedAt: DateTime | null

  @column.dateTime({ columnName: 'blocked_at' })
  public blockedAt: DateTime | null

  @column.dateTime({ columnName: 'rejected_at' })
  public rejectedAt: DateTime | null

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  // Relations
  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => PatientInsuranceCompany, {
    foreignKey: 'patientInsuranceCompanyId',
  })
  public patient_insurance: BelongsTo<typeof PatientInsuranceCompany>

  @belongsTo(() => InsuranceCompanySubscription, {
    foreignKey: 'insuranceCompanySubscriptionId',
  })
  public subscription: BelongsTo<typeof InsuranceCompanySubscription>

  @belongsTo(() => Diagnostic, {
    foreignKey: 'diagnosticId',
  })
  public diagnostic: BelongsTo<typeof Diagnostic>

  @belongsTo(() => Prescription, {
    foreignKey: 'prescriptionId',
  })
  public prescription: BelongsTo<typeof Prescription>

  @hasMany(() => InsuranceCompanyPrescriptionItem, {
    foreignKey: 'insuranceCompanyPrescriptionId',
  })
  public items: HasMany<typeof InsuranceCompanyPrescriptionItem>
}
