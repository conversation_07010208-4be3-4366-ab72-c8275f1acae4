import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import LaboratoryController from './LaboratoryController';
import { ApiResponse, QuotationNotification, QuotationVersion, ResultType } from 'App/Controllers/Utils/models';
import QuotationProposal from 'App/Models/QuotationProposal';
import Order from 'App/Models/Order';
import Delivery from 'App/Models/Delivery';
import Patient from 'App/Models/Patient';
import Database from '@ioc:Adonis/Lucid/Database';
import QuotationProposalItem from 'App/Models/QuotationProposalItem';
import NatService from 'App/Services/NatService';
import QuotationRequest from 'App/Models/QuotationRequest';
import QuotationPartner from 'App/Models/QuotationPartner';
import AnalyzeAsk from 'App/Models/AnalyzeAsk';
import Storage from 'App/Services/Storage';
import AnalyzeAskResult from 'App/Models/AnalyzeAskResult';
import AnalyzeAskResultItem from 'App/Models/AnalyzeAskResultItem';
import AnalyzeAskItem from 'App/Models/AnalyzeAskItem';

export default class AnalyzeAskQuotationController extends LaboratoryController {

  public async createAnalyzeAskQuotationProposition({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          quotation_request_id: schema.number(),
          items: schema.array().members(
            schema.object().members({
              analyze_ask_item_id: schema.number(),
              price: schema.number(),
            })
          ),
        })
      });
      const { quotation_request_id, items } = payload;
      const authUser = await auth.authenticate();
      const LaboratoryId = await this.getLaboratoryIdByPersonal(auth);
      if (LaboratoryId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const quotation = await QuotationRequest.query().where('id', quotation_request_id).forUpdate().first();
      if (!quotation) {
        status = 404;
        apiResponse = {
          message: "Demande de devis introuvable ou incorrect, veuille zréessayer plus tard",
          success: false,
          result: null,
          except: quotation,
        };
        return response.status(status).json(apiResponse);
      }

      let currentVersionId = Number(quotation.currentVersion);
      let versions: QuotationVersion[] = typeof quotation.versions === 'string' ? JSON.parse(quotation.versions as string) : quotation.versions;

      const partner = await QuotationPartner.query()
        .where('quotation_request_id', quotation.id)
        .andWhere('laboratory_id', LaboratoryId)
        .andWhere('status', 'pending')
        .andWhere('version_id', currentVersionId)
        .forUpdate()
        .first();

      if (!partner) {
        status = 404;
        apiResponse = {
          message: "Demande de devis introuvable ou incorrect, veuillez réessayer plus tard",
          success: false,
          result: null,
        };
        return response.status(status).json(apiResponse);
      }

      let notifications: QuotationNotification[] = typeof partner.notifications === 'string' ? JSON.parse(partner.notifications as string) : partner.notifications;

      const patient = await Patient.query().where('id', quotation.patientId).first();
      if (!patient) {
        status = 404;
        apiResponse = {
          message: "Patient introuvable, veuillez réessayez plus tard",
          success: false,
          result: null,
          except: patient,
        };
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        let reference = Math.floor(Math.random() * 100000000) * 900000000;
        let totalPrice = 0;
        items.forEach((item) => {
          totalPrice += item.price;
        });
        const proposal = await QuotationProposal.create({
          reference: reference.toString(),
          laboratoryId: LaboratoryId,
          quotationRequestId: quotation_request_id,
          proposalUserId: authUser.id,
          status: 'pending',
          totalPrice: totalPrice,
          totalItems: items.length,
          versionId: currentVersionId
        }, { client: trx });
        if (!proposal) {
          await trx.rollback();
          status = 500;
          apiResponse.message = "Une erreur serveur est survenue, veuillez réessayer plus tard";
          apiResponse.success = false;
          apiResponse.result = null;
          apiResponse.except = proposal;
          return response.status(status).json(apiResponse);
        }

        //get validated items from versions
        const validatedItems = versions
          .filter(version => version.status === 'pending' && version.version_id === currentVersionId)
          .map(version => version.items)
          .flat();

        // Vérifier que les éléments de la requête sont valides
        const validItems = items.filter(item => {
          return validatedItems.some(validItem => validItem.item_id === item.analyze_ask_item_id && validItem.type === 'analyze' );
        });

        if (validItems.length === 0) {
          await trx.rollback();
          status = 400;
          apiResponse = {
            message: "Aucun élément valide trouvé pour la version en cours",
            success: false,
            result: null,
          };
          return response.status(status).json(apiResponse);
        }

        //add items to quotation proposal
        const createdItems = validItems.map(item => ({
          quotationProposalId: proposal.id,
          analyzeAskItemId: item.analyze_ask_item_id,
          quantity: 1,
          unitPrice: item.price,
          status: 'pending' as 'pending',
        }));
        const proposalItems = await QuotationProposalItem.createMany(createdItems, { client: trx });
        if (!proposalItems) {
          await trx.rollback();
          console.log("error create proposal items", proposalItems);

          status = 500;
          apiResponse = {
            message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
            success: false,
            result: null,
            except: proposalItems,
          };
          return response.status(status).json(apiResponse);
        }

        // update notifications
        const notification = notifications.find(notification => notification.version_id === currentVersionId && notification.status === 'pending');
        if (notification && notification !== undefined ) {
          notification.status = 'accepted';
          notification.notified_at = new Date().toISOString();

          const updatedNotifications = partner.notifications ? [...notifications, notification] : [notification];
          await partner
            .useTransaction(trx)
            .merge({
              status: 'accepted',
              notifications: JSON.stringify(updatedNotifications),
          }).save();
        }

        await quotation
          .useTransaction(trx)
          .merge({
            status: 'proposal_received',
          }).save();

        await trx.commit();

        let patientChannel = patient.channel;
        if (patientChannel) {
          const proposalData = await QuotationProposal.query().where('laboratory_id', LaboratoryId).where('quotation_request_id', quotation_request_id).orderBy('created_at', 'desc')
            .preload('proposalUser')
            .preload('items')
            .preload('pharmacy')
            .preload('laboratory')
            .first();

          let data = {
            channel: patientChannel,
            proposal: proposalData,
          }
          //send to nats
          const nat = await NatService.getInstance();
          await nat.publish('quotation.proposal', data);
          // console.log("publish to nats");
        }
        apiResponse = {
          success: true,
          message: "Proposition de devis envoyée avec succès",
          result: {
            proposal: proposal,
            items: proposalItems,
          },
        };
        return response.status(status).json(apiResponse);

      } catch (error) {
        console.log("error in create quotation proposition", error);
        await trx.rollback();
        status = 500;
        apiResponse = {
          message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
          success: false,
          result: null,
          except: error.message,
          errors: error.messages
        }
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error in create quotation proposition", error);
      apiResponse = {
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        success: false,
        result: null,
        except: error.message,
        errors: error.messages
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async createAnalyzeAskResult({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 201;
  
    try {
      const payload = await request.validate({
        schema: schema.create({
          quotation_request_id: schema.number(),
          analyze_ask_id: schema.number(),
          is_view: schema.boolean.optional(),
          is_positive: schema.boolean.optional(),
          comment: schema.string.optional(),
          file_attachment: schema.file.optional({
            size: '12mb',
            extnames: ['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'txt', 'zip'],
          }),
          items: schema.array().members(
            schema.object().members({
              quotation_proposal_item_id: schema.number(),
              analyze_ask_item_id: schema.number(),
              result_type: schema.enum(['textual', 'boolean', 'numerical', 'category', 'file']),
              result_value: schema.number.optional(),
              result_text: schema.string.optional(),
              result_boolean: schema.boolean.optional(),
              result_category: schema.string.optional(),
              unity_id: schema.number.optional(),
              reference_min: schema.string.optional(),
              reference_max: schema.string.optional(),
              is_within_range: schema.boolean.optional(),
              is_view: schema.boolean.optional(),
              comment: schema.string.optional(),
              metadata: schema.object.optional().anyMembers(),
              result_file: schema.file.optional({
                size: '12mb',
                extnames: ['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'txt', 'zip'],
              }),
            })
          ),
        }),
      });
  
      const { quotation_request_id, analyze_ask_id, is_view, is_positive, comment, file_attachment, items } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        status = 401;
        apiResponse.message = "You are not authenticated";
        return response.status(status).json(apiResponse);
      }
  
      const laboratoryId = await this.getLaboratoryIdByPersonal(auth);
      if (laboratoryId === null) {
        status = 401;
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        return response.status(status).json(apiResponse);
      }
  
      const proId = await this.getAuthPersonal(auth).then((personal) => personal?.id);
  
      const quotationRequest = await QuotationRequest.query()
        .where('id', quotation_request_id)
        .orWhere('reference', quotation_request_id)
        .preload('analyzeAsk', (query) => {
          query.preload('items', (itemQuery) => {
            itemQuery.preload('analyze');
          });
        })
        .preload('orders', (query) => {
          query.orderBy('created_at', 'desc').where('laboratory_id', laboratoryId).preload('items');
        })
        .first();
  
      if (!quotationRequest) {
        status = 404;
        apiResponse.message = "La demande de devis n'existe pas";
        return response.status(status).json(apiResponse);
      }
  
      const analyzeAsk = await AnalyzeAsk.query()
        .where('id', analyze_ask_id)
        .where('result_is_defined', false)
        .preload('items')
        .forUpdate()
        .first();
  
      if (!analyzeAsk) {
        status = 404;
        apiResponse.message = "Demande d'analyse introuvable ou incorrecte, veuillez réessayer plus tard";
        return response.status(status).json(apiResponse);
      }

      if (analyzeAsk.resultIsDefined && analyzeAsk.resultStatus === 'completed') {
        status = 400;
        apiResponse.message = "Le résultat de cette analyse a déjà été défini";
        return response.status(status).json(apiResponse);
      }
  
      const analyze_ask_items = analyzeAsk.items;
      const orders = quotationRequest.orders;
      const orderItems = orders.map((order) => order.items).flat();
  
      const checkItem = items.every((item) =>
        orderItems.some((orderItem) => orderItem.analyzeAskItemId === item.analyze_ask_item_id)
      );
  
      if (!checkItem) {
        status = 400;
        apiResponse.message = "Un ou plusieurs éléments ne sont pas valides";
        return response.status(status).json(apiResponse);
      }
  
      let checkExistResult = await AnalyzeAskResult.query()
        .where('analyze_ask_id', analyze_ask_id)
        .preload('items')
        .first();
      
      const newItems = items.filter((item) => !checkExistResult?.items.some((existingItem) => existingItem.analyzeAskItemId === item.analyze_ask_item_id));
      console.log("new items", newItems);
      
  
      const trx = await Database.transaction();
      const storage = new Storage();
  
      try {
        let fileAttachmentPath = "";
        if (file_attachment) {
          const bucketName = 'analyze-asks';
          const fileName = `${Date.now()}-${file_attachment.clientName}`;
          const objectName = `/results/${fileName}`;
          const filePath = String(file_attachment.tmpPath);
          const metadata = {
            'Content-Type': file_attachment.type,
            'Content-Disposition': `attachment; filename=${fileName}`,
            size: file_attachment.size,
            extension: file_attachment.extname,
          };
          await storage.uploadFile(bucketName, objectName, filePath, metadata);
          fileAttachmentPath = objectName;
        }

        let analyzeAskResult: AnalyzeAskResult | null = null;
  
        if (!checkExistResult) {
          const token = (Math.floor(Math.random() * 1000000000000)).toString().padStart(12, '0');
          analyzeAskResult = await AnalyzeAskResult.create(
            {
              analyzeAskId: analyze_ask_id,
              diagnosticId: analyzeAsk.diagnosticId,
              proId: Number(proId),
              laboratoryId: laboratoryId,
              isPositive: is_positive,
              isView: is_view,        
              comment: comment,       
              fileAttachment: fileAttachmentPath,
              token,
            },
            { client: trx }
          );
          if (!analyzeAskResult) {
            if (fileAttachmentPath) {
              await storage.deleteFile('analyze-asks', fileAttachmentPath);
            }
            await trx.rollback();
            status = 500;
            apiResponse = {
              success: false,
              message: "Echec de création du résultat",
              result: null,
              except: analyzeAskResult,
            };
            return response.status(status).json(apiResponse);
          }
        }
  
        // Filtrer les nouveaux items (exclure ceux qui existent déjà)
        if (newItems.length === 0  ) {
          await trx.rollback();
          status = 400;
          apiResponse = {
            success: false,
            message: "Aucun nouvel élément ou fichier à ajouter",
            result: null,
          };
          return response.status(status).json(apiResponse);
        }
  
        // Création des nouveaux items
        const itemsToCreate = await Promise.all(
          newItems.map(async (item) => {
            const isPaid = analyze_ask_items.some((ai) => ai.id === item.analyze_ask_item_id && ai.isPaid);
  
            if (!isPaid) return null;
  
            let resultMediaPath = "";
            if (item.result_file) {
              const bucketName = 'analyze-asks';
              const fileName = `${Date.now()}-${item.result_file.clientName}`;
              const objectName = `/results/items/${fileName}`;
              const filePath = String(item.result_file.tmpPath);
              const metadata = {
                'Content-Type': item.result_file.type,
                'Content-Disposition': `attachment; filename=${fileName}`,
                size: item.result_file.size,
                extension: item.result_file.extname,
              };
              await storage.uploadFile(bucketName, objectName, filePath, metadata);
              resultMediaPath = objectName;
            }
  
            return {
              analyzeAskResultId: Number(analyzeAskResult?.id),
              analyzeAskItemId: item.analyze_ask_item_id,
              resultType: item.result_type as ResultType,
              resultValue: item.result_value,
              resultText: item.result_text,
              resultBoolean: item.result_boolean,
              resultCategory: item.result_category,
              unityId: item.unity_id,
              referenceMin: item.reference_min,
              referenceMax: item.reference_max,
              isWithinRange: item.is_within_range,
              isView: item.is_view,
              comment: item.comment,
              metadata: JSON.stringify(item.metadata),
              resultMediaPath: resultMediaPath,
            };
          })
        );
  
        // console.log("items to create",itemsToCreate);
        
        const validItems = itemsToCreate.filter((item) => item !== null);
        // console.log("valid items", validItems);
  
        if (validItems.length > 0) {
          const createdItems = await AnalyzeAskResultItem.createMany(validItems, { client: trx });
          if (!createdItems) {
            await trx.rollback();
            status = 400;
            apiResponse = {
              success: false,
              message: "Echec de création des éléments du résultat",
              result: null,
            };
            return response.status(status).json(apiResponse);
          }

          if (!createdItems || createdItems.length === 0) {
            // Gestion de l'erreur si aucun élément n'a été créé
            if (fileAttachmentPath) {
              await storage.deleteFile('analyze-asks', fileAttachmentPath);
            }
  
            for (const item of validItems) {
              if (item.resultMediaPath) {
                await storage.deleteFile('analyze-asks', item.resultMediaPath);
              }
            }
  
            await trx.rollback();
            status = 500;
            apiResponse = {
              success: false,
              message: "Aucun élément valide à créer",
              result: null,
            };
            return response.status(status).json(apiResponse);
          }
        }else{
          if (fileAttachmentPath) {
            await storage.deleteFile('analyze-asks', fileAttachmentPath);
          }
          console.log("error create proposal items", validItems);
          
          await trx.rollback();
          status = 400;
          apiResponse = {
            success: false,
            message: "Aucun élément valide à créer",
            result: null,
            except: validItems,
          };
          return response.status(status).json(apiResponse);
        }

        for(const item of newItems) {
          await AnalyzeAskItem.query()
            .where('id', item.analyze_ask_item_id)
            .update({ resultIsDefined: true })
          .useTransaction(trx);
        }

        // Vérifier si tous les items sont remplis pour définir resultIsDefined
        const allAnalyzeAskItems = analyze_ask_items;
        const existingItems = checkExistResult?.items;
        const allItemsFilled = allAnalyzeAskItems.every((ai) =>
          existingItems?.some((ei) => ei.analyzeAskItemId === ai.id)
        );
  
        analyzeAsk.resultIsDefined = true;
        analyzeAsk.resultStatus = allItemsFilled ? 'completed' : 'partially';
        await analyzeAsk.useTransaction(trx).save();

        await trx.commit();
  
        apiResponse = {
          success: true,
          message: "Résultat de l'analyse mis à jour avec succès",
          result: analyzeAskResult,
        };
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error in create analyze ask result", error);
        status = 500;
        apiResponse = {
          success: false,
          message: "Une erreur serveur est survenue",
          result: null,
          except: error.message,
        };
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error in create analyze ask result", error);
      apiResponse = {
        success: false,
        message: "Echec de mise à jour du résultat",
        result: null,
        except: error.message,
        errors: error.messages,
      };
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async getQuotationRequests({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      let quotationRequests = await QuotationRequest.query()
        .select('quotation_requests.*')
        .innerJoin('quotation_partners', 'quotation_partners.quotation_request_id', 'quotation_requests.id')
        .where('quotation_partners.laboratory_id', entityId)
        .preload('patient')
        .preload('analyzeAsk', function (query) {
          query.preload('items', function (query) {
            query.preload('analyze')
          })
        })
        .preload('proposals', function (query) {
          query.where('laboratory_id', entityId);
        })
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Quotation request fetched successfully",
        result: quotationRequests,
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getQuotationRequestDetails({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const quotationRequestId = request.input('quotation_request_id');
      if (!quotationRequestId) {
        apiResponse.message = "Veuillez renseignez l'identifiant de la demande";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const quotationRequest = await QuotationRequest.query().where('id', quotationRequestId).orWhere('reference', quotationRequestId)
        .preload('patient', function (query) {
          query.select(['id', 'first_name', 'last_name', 'phone', 'email','gender'])
        })
        .preload('analyzeAsk', function (query) {
          query.preload('items', function (ItemQuery) {
            ItemQuery.preload('analyze')
          })
        })
        .preload('proposals', function (query) {
          query.where('laboratory_id', entityId).orderBy('created_at', 'desc')
        })
        .preload('orders', function (query) {
          query.orderBy('created_at', 'desc').where('laboratory_id', entityId).preload('items')
        })
      .first();

      if (quotationRequest === null) {
        apiResponse.message = "La demande de devis n'existe pas";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const analyze_ask_status = await this.getAnalyzeAskStatus(quotationRequest);
      apiResponse = {
        success: true,
        message: "Détails de la demande de devis",
        result: {
          quotation: quotationRequest,
          analyze_ask_status: analyze_ask_status
        }
      }
    } catch (error) {
      console.log("error in get quotation request details", error);
      
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getQuotationPropositions({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const quotations = await QuotationProposal.query().where('laboratory_id', entityId).orderBy('created_at', 'desc')
        .preload('proposalUser').preload('items')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des propositions de devis",
        result: quotations
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getQuotationProposalDetails({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const propositionId = request.input('proposition_id');
      if (!propositionId) {
        apiResponse.message = "Veuillez renseignez l'identifiant de la proposition";
        status = 400;
      }
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const quotationProposal = await QuotationProposal.query().where('id', propositionId).where('laboratory_id', entityId)
        .preload('proposalUser')
        .preload('items')
        .preload('quotationRequest', function (query) {
          query.preload('patient')
          query.preload('analyzeAsk')
        })
        .first();

      if (!quotationProposal) {
        apiResponse.message = "La proposition de devis n'existe pas";
        status = 404;
      }

      apiResponse = {
        success: true,
        message: "Détails de la proposition de devis",
        result: quotationProposal
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getOrders({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const orders = await Order.query().where('laboratory_id', entityId).orderBy('created_at', 'desc').withCount('items')
        .preload('quotation_request').preload('analyze_ask', function (query) {
          query.preload('items', function (query) {
            query.preload('analyze')
          })
        })
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des commandes",
        result: orders
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getOrderDetails({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const orderId = request.input('order_id');
      if (!orderId) {
        apiResponse.message = "Veuillez renseignez l'identifiant de la commande";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const order = await Order.query().where('id', orderId).orWhere('reference', orderId).where('laboratory_id', entityId)
        .preload('quotation_request')
        .preload('items', function (query) {
          query.preload('analyze_ask_item', function (ItemQuery) {
            ItemQuery.preload('analyze')
          })
          .preload('quotation_proposal_item')
        })
        .preload('analyze_ask', function (query) {
          query.preload('items', function (query) {
            query.preload('analyze')
          })
        })
        .preload('proposition')
        .preload('patient')
        .first();

      if (!order) {
        apiResponse.message = "La demande de devis n'existe pas";
        status = 404;
      }
      apiResponse = {
        success: true,
        message: "Détails de la demande de devis",
        result: order
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getDeliveries({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const delivery_status = request.input('status') as 'pending' | 'delivered' | 'cancelled' | 'failed' | 'validated' | 'prepared' | 'in_progress';

      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      let query = Delivery.query().where('laboratory_id', entityId).where('delivery_type', 'analyze');
      if (delivery_status) {
        query = query.where('status', delivery_status)
      }

      const deliveries = await query.paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des livraisons",
        result: deliveries
      }

    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getAnalyzeAskResults({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const entityId = await this.getLaboratoryIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const analyzeAskResults = await AnalyzeAskResult.query().where('laboratory_id', entityId).orderBy('created_at', 'desc')
        .preload('pro')
        .preload('items')
        .preload('analyze_ask', function (query) {
          query.preload('patient')
        })
        .preload('diagnostic')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des résultats d'analyse",
        result: analyzeAskResults
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  

}
