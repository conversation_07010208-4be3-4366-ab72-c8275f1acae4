{"extends": "adonis-preset-ts/tsconfig.json", "include": ["**/*"], "exclude": ["node_modules", "build"], "compilerOptions": {"outDir": "build", "rootDir": "./", "baseUrl": "./", "sourceMap": true, "paths": {"App/*": ["./app/*"], "Config/*": ["./config/*"], "Contracts/*": ["./contracts/*"], "Database/*": ["./database/*"]}, "types": ["@adonisjs/core", "@adonisjs/repl", "@japa/preset-adonis/build/adonis-typings", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/view", "@adonisjs/mail", "@adonisjs/redis"]}}