import Route from '@ioc:Adonis/Core/Route';
import PharmacyController from 'App/Controllers/Http/api/core/pharma/PharmacyController';
import PrescriptionQuotationController from 'App/Controllers/Http/api/core/pharma/PrescriptionQuotationController';

const phmcyCtrl = new PharmacyController();
const quotationCtrl = new PrescriptionQuotationController();
Route.group(() => {
  Route.group(() => {
    Route.get('/entity',async (ctx) => {
      return await phmcyCtrl.getPharmacy(ctx);
    });
    Route.get('/personals',async (ctx) => {
      return await phmcyCtrl.getPharmacyPersonals(ctx);
    });
    Route.post('/personals/create',async (ctx) => {
      return await phmcyCtrl.addNewPersonal(ctx);
    });
    Route.post('/personals/update',async (ctx) => {
      return await phmcyCtrl.updatePersonal(ctx);
    });
    Route.post('/entity/update-location', async (ctx) => {
      return await phmcyCtrl.updatePharmacyLocation(ctx);
    })
    Route.get('/transactions',async (ctx) => {
      return await phmcyCtrl.getEntityTransactions(ctx);
    });

    Route.group(() => {
      Route.get('/quotations/latest-request',async (ctx) => {
        return await phmcyCtrl.getLatestQuotationRequest(ctx);
      });
      Route.get('/quotations/not-responded',async (ctx) => {
        return await phmcyCtrl.getQuotationRequestNotResponded(ctx);
      });
      Route.get('/',async (ctx) => {
        return await phmcyCtrl.getAnalyticalData(ctx);
      });
    }).prefix('analytics');

    Route.group(() => {
      Route.post('/simulate-proposal',async (ctx) => {
        return await quotationCtrl.simulateQuotationProposal(ctx);
      });
      Route.get('/requests',async (ctx) => {
        return quotationCtrl.getQuotationRequests(ctx);
      });
      Route.get('/requests/details',async (ctx) =>{
        return await quotationCtrl.getQuotationRequestDetails(ctx);
      });
      Route.get('/propositions',async (ctx) => {
        return await quotationCtrl.getQuotationProposals(ctx);
      });
      Route.post('/propositions/add',async (ctx) => {
        return await quotationCtrl.createQuotationProposal(ctx);
      });
      Route.get('/propositions/details',async (ctx) => {
        return await quotationCtrl.getQuotationPropositionDetails(ctx);
      });
      Route.get('/orders',async (ctx) => {
        return await quotationCtrl.getOrders(ctx);
      });
      Route.get('/orders/details',async (ctx) => {
        return await quotationCtrl.getOrderDetails(ctx);
      });
      Route.get('/deliveries',async (ctx) => {
        return await quotationCtrl.getDeliveries(ctx);
      });
    }).prefix('quotations');

  }).prefix('pharmacy');
}).prefix('api').namespace('App/Controllers/Http/api/core/pharma').middleware(['auth']);
