export function formatErrorResponse(error: { messages: { errors: any[]; }; }) {
  let message = "";

  if (error.messages && error.messages.errors && error.messages.errors.length > 0) {
    const errorMessages = error.messages.errors.map((element) => element.message);
    message = errorMessages.join(', ');
  } else {
    message = "Une erreur est survenue, veuillez réessayer.";
  }

  return message.trim();
}
