import Application from '@ioc:Adonis/Core/Application';
import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse, KYCRequest, ParrainageMetadata, UserParrainage } from 'App/Controllers/Utils/models';
import Laborantin from 'App/Models/Laborantin';
import Pharmacien from 'App/Models/Pharmacien';
import Soignant from 'App/Models/Soignant';
import { formatErrorResponse } from 'App/Controllers/Utils';
import HelperController from '../helpers/HelperController';
import Wallet from 'App/Models/Wallet';
import User from 'App/Models/User';
import Storage from 'App/Services/Storage';
import ParrainageSetting from 'App/Models/ParrainageSetting';
import Monetization from 'App/Models/Monetization';
import Reversal from 'App/Models/Reversal';

export default class ProfileController extends HelperController {

  public async getProfile({ auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 200;
    try {
      const authUser = await auth.authenticate();
      if (authUser) {
        let roleId = authUser.roleId;
        const user = await User.query().where('id', authUser.id).preload('role').first();
        const wallet = await Wallet.query().where('user_id', authUser.id).first();
        let entity = {} as Soignant | Pharmacien | Laborantin;
        switch (roleId) {
          case 3:
            entity = await Soignant.query().where('user_id', authUser.id).first() as Soignant;
            break;
          case 4:
            entity = await Pharmacien.query().where('user_id', authUser.id).first() as Pharmacien;
            break;
          case 5:
            entity = await Laborantin.query().where('user_id', authUser.id).first() as Laborantin;
            break;
          default:
            break;
        }

        let userParrainage: UserParrainage = authUser.parrainage;
        let metadata = {} as any;
        if (userParrainage) {
          const plan = await ParrainageSetting.query().where('id', userParrainage.plan).first();
          const monetization = await Monetization.query().where('user_id', authUser.id).first();
          const lastReversal = await Reversal.query().where('user_id', authUser.id).orderBy('created_at', 'desc').first();
          metadata = {
            plan: plan,
            parrainage: userParrainage,
            monetization: monetization,
            lastReversal: lastReversal
          }
        }

        apiResponse = {
          success: true,
          message: "Profile",
          result: {
            entity: entity,
            user: user,
            role: user?.role,
            wallet: wallet,
            metadata: metadata
          },
        }
        status = 200;
      } else {
        apiResponse = {
          success: false,
          message: "User profile not found",
          result: null as any,
          except: null as any
        }
        status = 401;
      }
    } catch (error) {
      console.log("error in get user profile", error);
      apiResponse = {
        success: false,
        message: "Une erreur est survenue, veuillez réessayer.",
        result: null as any,
        except: null as any
      }
      status = 500;
    }
    return response.status(status).send(apiResponse);
  }

  public async updateUserProfil({ request, response, auth }: HttpContextContract) {
    let apiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          username: schema.string.optional({ trim: true }),
          email: schema.string.optional({ trim: true }),
          phone: schema.string.optional({ trim: true }),
          language_id: schema.number.optional(),
          profileIMG: schema.file.optional({
            size: '2mb',
            extnames: ['jpg', 'png', 'jpeg'],
          })
        })
      });
      const { username, email, language_id, profileIMG, phone } = payload;
      const authUser = await auth.authenticate();
      if (authUser) {
        //store file
        if (profileIMG) {
          let fileName = `${Date.now()}-${profileIMG.clientName}`;
          let fileExt = profileIMG.extname;
          await profileIMG.move(Application.tmpPath('uploads/profiles'), {
            name: `${fileName}.${fileExt}`,
            overwrite: true
          });
          authUser.profileIMG = `${fileName}.${fileExt}`;
        }

        authUser.username = String(username);
        authUser.email = String(email);
        authUser.phone = String(phone);
        authUser.languageId = Number(language_id);
        await authUser.save();
        const entity = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id);
        if (entity) {
          if (email) {
            entity.email = email;
          }
          if (phone) {
            entity.phone = phone;
          }
          await entity?.save();
        }

        apiResponse = {
          success: true,
          message: "Profil mis à jour avec succès",
          result: authUser,
          except: null as any
        }
      } else {
        apiResponse = {
          success: false,
          message: "User not found",
          result: null as any,
          except: null as any
        }
      }
    } catch (error) {
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async updateAccount({ request, response, auth }: HttpContextContract) {
    let apiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };

    try {
      // Validation des données de la requête
      const payload = await request.validate({
        schema: schema.create({
          last_name: schema.string.optional({ trim: true }),
          first_name: schema.string.optional({ trim: true }),
          email: schema.string.optional({ trim: true }),
          phone: schema.string.optional({ trim: true }),
          country_id: schema.number.optional(),
          city_id: schema.number.optional(),
          quarter_id: schema.number.optional(),
          birthday_year: schema.number.optional(),
          birthday_month: schema.number.optional(),
          birthday_day: schema.number.optional(),
          profession: schema.string.optional({ trim: true }),
          health_institute_id: schema.number.optional(),
          pharmacy_id: schema.number.optional(),
          laboratory_id: schema.number.optional(),
          domain_id: schema.number.optional(),
          gender: schema.enum.optional(['M', 'F']),
          departement: schema.string.optional(),
          address: schema.array.optional().members(
            schema.object().members({
              libelle: schema.string.optional({ trim: true }),
              lat: schema.string.optional({ trim: true }),
              long: schema.string.optional({ trim: true }),
            })
          ),
        }),
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "User not found";
        return response.status(404).json(apiResponse);
      }

      const entity = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id);
      if (!entity) {
        apiResponse.message = "User account not found";
        return response.status(404).json(apiResponse);
      }

      // Mise à jour des champs communs uniquement si une nouvelle valeur est fournie
      const fieldsToUpdate = [
        'last_name', 'first_name', 'email', 'phone', 'country_id', 'city_id', 'quarter_id',
        'birthday_year', 'birthday_month', 'birthday_day', 'profession', 'domain_id', 'gender',
      ];

      fieldsToUpdate.forEach((field) => {
        if (payload[field] !== undefined && payload[field] !== null) {
          // Conversion explicite pour les champs numériques
          if (typeof payload[field] === 'number' || !isNaN(Number(payload[field]))) {
            entity[field] = Number(payload[field]);
          } else if (typeof payload[field] === 'string') {
            entity[field] = payload[field].trim();
          } else {
            entity[field] = payload[field];
          }
        }
      });

      // Mise à jour des champs spécifiques à chaque rôle uniquement si une nouvelle valeur est fournie
      if (entity instanceof Soignant) {
        if (payload.health_institute_id !== undefined && !isNaN(Number(payload.health_institute_id))) {
          entity.healthInstituteId = Number(payload.health_institute_id);
        }
        if (payload.departement !== undefined) {
          entity.departement = String(payload.departement);
        }
      }

      if (entity instanceof Pharmacien && payload.pharmacy_id !== undefined && !isNaN(Number(payload.pharmacy_id))) {
        entity.pharmacyId = Number(payload.pharmacy_id);
      }

      if (entity instanceof Laborantin && payload.laboratory_id !== undefined && !isNaN(Number(payload.laboratory_id))) {
        entity.laboratory_id = Number(payload.laboratory_id);
      }

      // Mise à jour des champs de l'utilisateur authentifié uniquement si une nouvelle valeur est fournie
      if (payload.phone !== undefined) {
        authUser.phone = String(payload.phone);
      }
      if (payload.email !== undefined) {
        authUser.email = String(payload.email);
      }

      // Sauvegarde des modifications
      await entity.save();
      await authUser.save();

      apiResponse.success = true;
      apiResponse.message = "Account updated successfully";
      apiResponse.result = entity;
    } catch (error) {
      console.log("Error updating account:", error);
      apiResponse.message = formatErrorResponse(error);
      apiResponse.except = error.message;
    }

    return response.status(apiResponse.success ? 200 : 500).json(apiResponse);
  }

  public async updateUserLocation({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          country_id: schema.number.optional(),
          city_id: schema.number.optional(),
          quarter_id: schema.number.optional()
        })
      });
      const {country_id, city_id, quarter_id} = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const entity = await Soignant.query().where('user_id', authUser.id).forUpdate().first();
      if (!entity) {
        apiResponse.message = "User account not found";
        return response.status(404).json(apiResponse);
      }

      await entity.merge({
        countryId: country_id !== undefined ? Number(country_id) : entity.countryId,
        cityId: city_id !== undefined ? Number(city_id) : entity.cityId,
        quarterId: quarter_id !== undefined ? Number(quarter_id) : entity.quarterId
      }).save();

      apiResponse.success = true;
      apiResponse.message = "Données mises à jour";
      apiResponse.result = entity;
      
      return response.status(200).json(apiResponse);
    } catch (error) {
      console.log("Error updating account:", error);
      apiResponse.message = formatErrorResponse(error);
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  /**
   * sendRequestAccountKYC function
   * Envoyer une demande de validation du compte
   * @param param0
   * @returns
   */
  public async sendRequestAccountKYC({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          pieceType: schema.string({ trim: true }), // CNI, PERMIS, PASSEPORT
          numPiece: schema.string({ trim: true }),
          dateExpire: schema.string.optional(),
          type_attestation: schema.string({ trim: true }), // Attestation CNI, Attestation diplome, Attestation carte professionnelle
          attestation_number: schema.string.optional({ trim: true }),
          attestation_img: schema.file.optional({
            size: '12mb',
            extnames: ['jpg', 'png', 'jpeg', 'pdf'],
          }),
          profesional_doc: schema.file.optional({
            size: '12mb',
            extnames: ['jpg', 'png', 'jpeg', 'pdf'],
          }),
          cni_img: schema.file.optional({
            size: '12mb',
            extnames: ['jpg', 'png', 'jpeg', 'pdf'],
          }),
        })
      });
      const { pieceType, numPiece, dateExpire, type_attestation, attestation_number, attestation_img, cni_img, profesional_doc } = payload;
      const authUser = await auth.authenticate();
      if (authUser) {
        const entity = await this.getEntityPersonalByRoleId(authUser.roleId, authUser.id);
        if (entity) {
          // Récupérer les documents existants
          let entityDocs = entity.docs || {}; // Assurez-vous que entityDocs est un objet et non null
          let bucketName = 'soignants';
          const storage = new Storage();
          // Créer l'objet KYCRequest avec les nouvelles valeurs
          let updatedDocs: Partial<KYCRequest> = {
            pieceType: pieceType,
            numPiece: numPiece,
            dateExpire: dateExpire,
            type_attestation: type_attestation,
            attestation_number: attestation_number,
          };

          // Gérer l'image de l'attestation
          if (attestation_img) {
            let fileName = `${Date.now()}-${attestation_img.clientName}`;
            let fileExt = attestation_img.extname;
            const objectName = `/attestations/${fileName}`;
            const filePath = String(attestation_img.tmpPath);
            let metadata = {
              'Content-Type': attestation_img.type,
              'Content-Disposition': `attachment; filename=${fileName}`,
              size: attestation_img.size,
              extension: attestation_img.extname,
            }
            await storage.uploadFile(bucketName, objectName, filePath, metadata);
            updatedDocs.attestation_img = `${fileName}.${fileExt}`;
          }

          // Gérer l'image de la CNI
          if (cni_img) {
            let fileName = `${Date.now()}-${cni_img.clientName}`;
            let fileExt = cni_img.extname;
            const objectName = `/identities/${fileName}`;
            const filePath = String(cni_img.tmpPath);
            let metadata = {
              'Content-Type': cni_img.type,
              'Content-Disposition': `attachment; filename=${fileName}`,
              size: cni_img.size,
              extension: cni_img.extname,
            }
            await storage.uploadFile(bucketName, objectName, filePath, metadata);
            updatedDocs.cni_img = `${fileName}.${fileExt}`;
          }

          //professional_doc
          if (profesional_doc) {
            let fileName = `${Date.now()}-${profesional_doc.clientName}`;
            let fileExt = profesional_doc.extname;
            const objectName = `/profesionals/${fileName}`;
            const filePath = String(profesional_doc.tmpPath);
            let metadata = {
              'Content-Type': profesional_doc.type,
              'Content-Disposition': `attachment; filename=${fileName}`,
              size: profesional_doc.size,
              extension: profesional_doc.extname,
            }
            await storage.uploadFile(bucketName, objectName, filePath, metadata);
            updatedDocs.profesional_doc = `${fileName}.${fileExt}`;
          }


          entityDocs = updatedDocs;
          // Mettre à jour le statut de l'entité
          entity.docs = JSON.stringify(entityDocs);
          entity.status = 'kyc';
          // Sauvegarder l'entité
          await entity.save();

          apiResponse = {
            success: true,
            message: "Attestation mise à jour avec succès",
            result: entity as any,
          };
        }

      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error.message
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async checkUserMonetizationRequest(user: User) {
    let is_valid = false;
    const staterPlan = await ParrainageSetting.first();
    if (!staterPlan || staterPlan == null) {
      return is_valid = false;
    }
    const userParrainage: UserParrainage = user.parrainage;
    if (userParrainage && userParrainage != null) {
      let metadata: ParrainageMetadata[] = staterPlan.metadata;
      let qrcodeData = metadata.find((item: ParrainageMetadata) => item.key === 'qrcode');
      let adhesionData = metadata.find((item: ParrainageMetadata) => item.key === 'adhesion');

      if (qrcodeData !== undefined && adhesionData !== undefined) {
        is_valid = userParrainage.active_qrcode >= qrcodeData.value && userParrainage.adhesion_fees >= adhesionData.value;
      }
    }
    return is_valid;
  }

  public async addMonetizationRequest({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const payload = await request.validate({
        schema: schema.create({
          type_piece: schema.enum(['CNI', 'PASSEPORT', 'PERMIS']),
          pieceIMG: schema.file.optional({
            extnames: ['jpg', 'png', 'jpeg'],
          }),
          pieceID: schema.string(),
          delivered_at: schema.date(),
          expired_at: schema.date(),
          paymentMethod: schema.enum(['CASH', 'WALLET', 'CARD', 'MOBILE']),
          phoneNumber: schema.string.optional(),
          cardNumber: schema.string.optional(),
          cardExpiryDate: schema.date.optional(),
          cardCVV: schema.string.optional(),
        })
      });

      const { type_piece, pieceIMG, pieceID, delivered_at, expired_at, paymentMethod, phoneNumber, cardNumber, cardExpiryDate, cardCVV } = payload;

      const checkRequest = await this.checkUserMonetizationRequest(authUser);
      if (!checkRequest) {
        apiResponse.message = "Echec de la demande de monétisation, veuillez boucler le premier plan de parrainage";
        return response.status(400).json(apiResponse);
      }
      const checkExist = await Monetization.query().where('user_id', authUser.id).where('status', 'PENDING').first();
      if (checkExist) {
        apiResponse.message = "Echec de la demande de monétisation, vous avez deja une demande en cours";
        return response.status(400).json(apiResponse);
      }
      let fileName = '';
      if (request.file('pieceIMG') && pieceIMG) {
        const storage = new Storage();
        fileName = new Date().getTime() + "_" + pieceIMG.clientName;
        const objectName = `/parrainage/${fileName}`;
        const filePath = String(pieceIMG.tmpPath);

        let bucketName = 'patients';
        let metadata = {
          'Content-Type': pieceIMG.type,
          'Content-Disposition': `attachment; filename=${fileName}`,
          size: pieceIMG.size,
          extension: pieceIMG.extname,
        }
        await storage.uploadFile(bucketName, objectName, filePath, metadata);
      }

      let pieceData = {
        pieceID: pieceID,
        delivered_at: delivered_at,
        expired_at: expired_at,
        pieceIMG: fileName,
        type_piece: type_piece,
      }

      let paymentData = {
        phone_number: phoneNumber,
        card_number: cardNumber,
        card_expiry_date: cardExpiryDate,
        card_cvv: cardCVV,
      }
      let balance = {
        currency: "XOF",
        solde: 0,
        last_update: new Date(),
      }
      let codeRef = await this.generateToken();
      const monetizationRequest = await Monetization.create({
        userId: authUser.id,
        pieceData: JSON.stringify(pieceData),
        paymentData: JSON.stringify(paymentData),
        balance: JSON.stringify(balance),
        paymentMethod: paymentMethod as any,
        status: 'PENDING',
        code: codeRef
      });

      if (!monetizationRequest) {
        apiResponse.message = "Echec de la demande de monétisation";
        apiResponse.except = monetizationRequest;
        return response.status(400).json(apiResponse);
      }

      apiResponse = {
        success: true,
        message: "Demande de monétisation envoyée avec succès",
        result: monetizationRequest,
      }
      return response.status(201).json(apiResponse);

    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
      return response.status(500).json(apiResponse);
    }
  }

  public async addNewReversalRequest({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const payload = await request.validate({
        schema: schema.create({
          amount: schema.number(),
        })
      });

      const { amount } = payload;

      let reference = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      let monetization = await Monetization.query().where('user_id', authUser.id).where('status', 'ACTIVE').first();
      if (!monetization) {
        apiResponse.message = "Echec de la demande de reversement, vous devez avoir une monétisation active";
        return response.status(400).json(apiResponse);
      }
      if (monetization.balance.solde < amount) {
        apiResponse.message = "Echec de la demande de reversement, votre solde est insuffisant";
        return response.status(400).json(apiResponse);
      }

      const reversalRequest = await Reversal.create({
        userId: authUser.id,
        amount: amount,
        reference: reference,
        monetizationId: monetization.id,
        status: 'PENDING',
        requestAt: new Date(),
      });

      if (!reversalRequest) {
        apiResponse.message = "Echec de la demande de reversement";
        apiResponse.except = reversalRequest;
        return response.status(400).json(apiResponse);
      }

      apiResponse = {
        success: true,
        message: "Demande de reversement envoyée avec succès",
        result: reversalRequest,
      }
      return response.status(201).json(apiResponse);
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
      return response.status(500).json(apiResponse);
    }
  }

  public async getReversalRequests({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 50);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const reversalRequests = await Reversal.query().where('user_id', authUser.id).where('status', 'PENDING').preload('monetization').orderBy('created_at', 'desc').paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Historique des demande de reversement",
        result: reversalRequests,
      }
      return response.status(201).json(apiResponse);
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
      return response.status(500).json(apiResponse);
    }
  }

  public async getUserParrainage({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    let status = 200;
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const codeParrainage = authUser.codeParrainage;
      let userParrainage: UserParrainage = authUser.parrainage;
      let metadata = {} as any;
      if (userParrainage) {
        const plan = await ParrainageSetting.query().where('id', userParrainage.plan).first();
        const monetization = await Monetization.query().where('user_id', authUser.id).first();
        const lastReversal = await Reversal.query().where('user_id', authUser.id).orderBy('created_at', 'desc').first();
        metadata = {
          plan: plan,
          parrainage: userParrainage,
          monetization: monetization,
          lastReversal: lastReversal
        }
        apiResponse = {
          success: true,
          message: "Profil du patient récupérer avec succès",
          result: {
            codeParrainage: codeParrainage,
            metadata: metadata
          }
        }
      }else{
        apiResponse = {
          success: true,
          message: "Profil du patient récupérer avec succès",
          result: {
            codeParrainage: codeParrainage,
            metadata: {}
          }
        }
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(status).json(apiResponse);
  }


}
