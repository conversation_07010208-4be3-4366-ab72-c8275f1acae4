import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, Has<PERSON>any, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Diagnostic from './Diagnostic'
import AnalyzeAskItem from './AnalyzeAskItem'
import Patient from './Patient'
import QuotationRequest from './QuotationRequest'
import { HasOne } from '@ioc:Adonis/Lucid/Orm'
import { hasOne } from '@ioc:Adonis/Lucid/Orm'
import Soignant from './Soignant'

export default class AnalyzeAsk extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'pro_id' })
  public proId: number

  @column({ columnName: 'fmd_id' })
  public fmdId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'reason' })
  public reason: string

  @column({ columnName: 'used_insurance' })
  public usedInsurance: boolean

  @column.dateTime({columnName: 'validated_at'})
  public validateAt: DateTime | null

  @column.dateTime({columnName: 'blocked_at'})
  public blockedAt: DateTime | null

  @column({ columnName: 'payment_status' })
  public paymentStatus: 'pending' | 'paid' | 'unpaid' | 'cancelled' | 'failed' | 'refunded'

  @column({ columnName: 'result_is_defined' })
  public resultIsDefined: boolean | null

  @column({ columnName: 'result_status' })
  public resultStatus: 'pending' | 'partially' | 'completed' = 'pending'

  @column({columnName: 'is_requested'})
  public isRequested: boolean | null

  @belongsTo(() => Diagnostic, {
    foreignKey: 'diagnosticId',
    localKey: 'id',
  })
  public diagnostic: BelongsTo<typeof Diagnostic>

  @belongsTo(() => Soignant, {
    foreignKey: 'proId',
    localKey: 'id',
  })
  public pro: BelongsTo<typeof Soignant>

  @hasMany(() => AnalyzeAskItem, {
    foreignKey: 'analyzeAskId',
    localKey: 'id',
  })
  public items: HasMany<typeof AnalyzeAskItem>

  @belongsTo(()=>Patient,{foreignKey: 'patientId',localKey: 'id'})
  public patient: BelongsTo<typeof Patient>

  @hasOne(() => QuotationRequest, {
    foreignKey: 'analyzeAskId',
    localKey: 'id'
  })
  public quotation_request: HasOne<typeof QuotationRequest>
}
