import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import HelperController from "../../helpers/HelperController";
import { ApiResponse } from 'App/Controllers/Utils/models';
import Soignant from 'App/Models/Soignant';
import Personal from 'App/Models/Personal';
import Appointment from 'App/Models/Appointment';

export default class HealthProController extends HelperController {

  public async getHealthInstitutes({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pro = await Soignant.query().where('user_id', authUser.id).first();
      if (!pro) {
        apiResponse.message = "Personnel introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const healthInstitutes = await Personal.query().where('soignant_id', pro.id)
        .preload('healthInstitute')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Health institutes",
        result: healthInstitutes,
      };
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.except = error;
      status = 500;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  public async getProHealthInstitutes({  response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any
    }
    let status = 200;
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pro = await Soignant.query().where('user_id', authUser.id).first();
      if (!pro) {
        apiResponse.message = "Personnel introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const queries = await Personal.query().where('soignant_id', pro.id).preload('healthInstitute');

      const healthInstitutes =  queries.map((query) => {
        return query.healthInstitute;
      });

      apiResponse = {
        success: true,
        message: "Pro Health institutes",
        result: healthInstitutes,
      };
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getAppointments({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à accéder à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pro = await Soignant.query().where('user_id', authUser.id).first();
      if (!pro) {
        apiResponse.message = "Personnel introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const appointments = await Appointment.query().where('pro_id', pro.id)
        .preload('healthInstitute').preload('patient').preload('appointmentType').paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste de mes rendez-vous",
        result: appointments,
      };
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }



}
