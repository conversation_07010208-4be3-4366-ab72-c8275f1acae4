import Route from '@ioc:Adonis/Core/Route';
import AnalyzeAskQuotationController from 'App/Controllers/Http/api/core/labo/AnalyzeAskQuotationController';
import LaboratoryController from 'App/Controllers/Http/api/core/labo/LaboratoryController';

const laboCtrl = new LaboratoryController();
const quotationCtrl = new AnalyzeAskQuotationController();
Route.group(() => {
  Route.group(() => {
    Route.get('/entity',async (ctx) => {
      return await laboCtrl.getLaboratory(ctx);
    });
    Route.get('/personals',async (ctx) => {
      return await laboCtrl.getEntityPersonals(ctx);
    });
    Route.post('/personals/create',async (ctx) => {
      return await laboCtrl.addNewPersonal(ctx);
    });
    Route.post('/entity/update-location', async (ctx) => {
      return await laboCtrl.updateLaboratoryLocation(ctx);
    })
    Route.get('/transactions',async (ctx) => {
      return await laboCtrl.getEntityTransactions(ctx);
    });

    Route.group(() => {
      Route.get('/',async (ctx) => {
        return await laboCtrl.getAnalyticalData(ctx);
      });
      Route.get('/quotations/latest-request',async (ctx) => {
        return await laboCtrl.getLatestQuotationRequests(ctx);
      });
      Route.get('/analyze-asks/pending-result',async (ctx) => {
        return await laboCtrl.getAnalyzeAskPendingResult(ctx);
      });
      Route.get('/analyze-asks/result',async (ctx) => {
        return await laboCtrl.getAnalyzeResultByAnalyzeAsk(ctx);
      });
    }).prefix('analytics');

    Route.group(() => {
      
      Route.group(() => {
        Route.get('/',async (ctx) => {
          return await quotationCtrl.getQuotationRequests(ctx);
        });
        Route.get('/details',async (ctx) => {
          return await quotationCtrl.getQuotationRequestDetails(ctx);
        });
      }).prefix('requests');

      Route.group(() => {
        Route.get('/',async (ctx) => {
          return await quotationCtrl.getQuotationPropositions(ctx);
        });
        Route.post('/create',async (ctx) => {
          return await quotationCtrl.createAnalyzeAskQuotationProposition(ctx);
        });
        Route.get('/details',async (ctx) => {
          return await quotationCtrl.getQuotationProposalDetails(ctx);
        });
      }).prefix('propositions');

      Route.group(() => {
        Route.get('/',async (ctx) => {
          return await quotationCtrl.getOrders(ctx);
        });
        Route.get('/details',async (ctx) => {
          return await quotationCtrl.getOrderDetails(ctx);
        });
        Route.post('/analyze-asks/add-result',async (ctx) => {
          return await quotationCtrl.createAnalyzeAskResult(ctx);
        });
        Route.get('/analyze-asks/results',async (ctx) => {
          return await quotationCtrl.getAnalyzeAskResults(ctx);
        });
      }).prefix('orders');

      Route.get('/deliveries',async (ctx) => {
        return await quotationCtrl.getDeliveries(ctx);
      });
    }).prefix('quotations');
    
  }).prefix('laboratory')
}).prefix('api').namespace('App/Controllers/Http/api/core/labo').middleware(['auth']);
