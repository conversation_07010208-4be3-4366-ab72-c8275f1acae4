import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class AnalyzeResultDeliver extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @column({ columnName: 'analyze_result_id' })
  public analyzeResultId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'doctor' })
  public doctor: string

  @column({ columnName: 'analyze_receipt' })
  public analyzeReceipt: string

  @column({ columnName: 'date_deliver' })
  public dateDeliver: DateTime

  @column({ columnName: 'status' })
  public status: string
}
