import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Laboratory from './Laboratory'
import Quarter from './Quarter'
import City from './City'
import Country from './Country'

export default class Laborantin extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string | null;

  @column({ columnName: 'address' })
  public address: string | null;

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number

  @column({ columnName: 'quarter_id' })
  public quarterId: number

  @column({ columnName: 'gender' })
  public gender: string

  @column({ columnName: 'profession' })
  public profession: string

  @column({ columnName: 'birthday_year' })
  public birthdayYear: number

  @column({ columnName: 'birthday_month' })
  public birthdayMonth: number

  @column({ columnName: 'birthday_day' })
  public birthdayDay: number

  @column({ columnName: 'domain_id' })
  public domainId: number

  @column({ columnName: 'docs' })
  public docs: any


  @column()
  public laboratory_id: number

  @column()
  public status: string

  @column()
  public schedules: any

  @column()
  public code: string

  @column({columnName: 'role'})
  public role: string;

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Laboratory,{
    foreignKey: 'laboratory_id',
    localKey: 'id'
  })
  public laboratory: BelongsTo<typeof Laboratory> 

  @belongsTo(() => Country,{
    foreignKey: 'countryId',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country> 

  @belongsTo(() => City,{
    foreignKey: 'cityId',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City> 

  @belongsTo(() => Quarter,{
    foreignKey: 'quarterId',
    localKey: 'id'
  })
  public district: BelongsTo<typeof Quarter>
}
