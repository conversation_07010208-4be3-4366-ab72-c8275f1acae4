import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import Country from './Country';
import Quarter from './Quarter';

export default class City extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'name' })
  public name: string;

  @column({ columnName: 'country_id' })
  public countryId: number;

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Country, { foreignKey: 'countryId' })
  public country: BelongsTo<typeof Country>

  @hasMany(() => Quarter)
  public quarters: HasMany<typeof Quarter>

}
