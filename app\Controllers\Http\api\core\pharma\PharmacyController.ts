
import { schema, rules } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import HelperController from "../../helpers/HelperController";
import { ApiResponse, UserStatus } from 'App/Controllers/Utils/models';
import Pharmacien from 'App/Models/Pharmacien';
import Database from '@ioc:Adonis/Lucid/Database';
import Wallet from 'App/Models/Wallet';
import { AuthContract } from '@ioc:Adonis/Addons/Auth';
import User from 'App/Models/User';
import WalletHistory from 'App/Models/WalletHistory';
import Pharmacy from 'App/Models/Pharmacy';
import QuotationRequest from 'App/Models/QuotationRequest';
export default class PharmacyController extends HelperController {

  public async getAuthPersonal(auth: AuthContract) {
    const authUser = await auth.authenticate();
    if (!authUser) {
      return null;
    }
    const pharmacien = await Pharmacien.query().where('user_id', authUser.id).preload('pharmacy').first();
    return pharmacien ? pharmacien : null;
  }

  public async getPharmacyIdByPersonal(auth: AuthContract) {
    const personal = await this.getAuthPersonal(auth);
    if (!personal) {
      return null;
    }
    return personal.pharmacyId;
  }

  public async getPharmacy({ auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const pharmacien = await this.getAuthPersonal(auth);
      if (!pharmacien) {
        status = 404;
        apiResponse = {
          message: "Le compte est introuvable",
          success: false,
          result: null,
        }
        return response.status(status).json(apiResponse);
      }
      const entity = pharmacien.pharmacy;
      if (entity) {
        const wallet = await Wallet.query().where('owner_id', entity.id).where('owner_type', 'pharmacy').andWhere('type_wallet_id', 3).first();
        apiResponse = {
          success: true,
          message: "Compte de l'entité pharmacie retrouvée avec succès",
          result: {
            entity: entity,
            wallet: wallet
          }
        }
      }
    } catch (error) {
      status = 500;
      console.log("error in create quotation proposal", error.message);
      apiResponse = {
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        success: false,
        result: null,
        except: error.message,
        errors: error.messages
      }
    }
    return response.status(status).json(apiResponse);
  }


  public async getPharmacyPersonals({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        status = 401;
        apiResponse.message = "Veuillez vous authentifié pour continuer l'opération";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }
      const pharmacyId = personal.pharmacyId;
      const personals = await Pharmacien.query().where('pharmacyId', pharmacyId)
        .preload('country').preload('city').preload('quarter').preload('user')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Données récupérées",
        result: personals,
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async addNewPersonal({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          last_name: schema.string(),
          first_name: schema.string(),
          email: schema.string.nullable({ trim: true }, [
            rules.email()
          ]),
          phone: schema.string(),
          country_id: schema.number(),
          city_id: schema.number.nullable(),
          quarter_id: schema.number.nullable(),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          gender: schema.enum(["M", "F"]),
          address: schema.string.optional(),
          profession: schema.string.optional(),
          role: schema.enum.optional(['manager', 'pharmacist', 'caisse', 'comptable']),
          password: schema.string(),
        })
      });

      const {
        last_name, first_name, email, phone, country_id, birthday_year, birthday_month, birthday_day, gender, address, profession,
        role, password, city_id, quarter_id
      } = payload;

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        status = 401;
        apiResponse.message = "Veuillez vous authentifié pour continuer l'opération";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }
      const pharmacyId = personal.pharmacyId;
      const pharmacy = await Pharmacy.query().where('id', pharmacyId).first();
      if (!pharmacy) {
        status = 404;
        apiResponse.message = "Le compte est introuvable";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }
      const check_exist_user = await User.query().where('phone', phone).orWhere('email', String(email)).first();

      if (check_exist_user) {
        apiResponse = {
          success: false,
          message: "Echec, le compte existe déjà avec cet email ou ce numéro de téléphone",
          result: null as any,
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const trx = await Database.transaction();
      try {
        let codeP = await this.generateCodeParrainage(8);
        const codeParrainage = {
          create_account: 0,
          active_qrcode: 0,
          adhesion_fees: 0,
          plan: 1,
          activeMoney: false
        }
        let username = `${last_name} ${first_name}`;
        const user = await User.create({
          username: username,
          phone: phone,
          email: email,
          password: password,
          countryId: country_id,
          roleId: 4,
          status: UserStatus.Actived,
          codeParrainage: codeP,
          parrainage: JSON.stringify(codeParrainage)
        }, { client: trx });

        if (!user) {
          apiResponse = {
            success: false,
            message: "Echec de création du compte utilisateur",
            result: null,
          }
          status = 500;
          await trx.rollback();
          return response.status(status).json(apiResponse);
        }
        const code = await this.generateToken();

        const pharmacien = await Pharmacien.create({
          userId: user.id,
          lastName: last_name,
          firstName: first_name,
          phone: phone,
          email: email,
          countryId: country_id,
          cityId: city_id ?? null,
          quarterId: quarter_id,
          gender: gender,
          birthdayYear: birthday_year,
          birthdayMonth: birthday_month,
          birthdayDay: birthday_day,
          code: code,
          status: 'validated',
          pharmacyId: pharmacyId,
          profession: profession,
          address: address,
          role: role,
        }, { client: trx });

        if (!pharmacien) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Une erreur serveur est survenue lors de la création du compte, veuillez réessayer plus tard",
            result: null,
            except: pharmacien,
          }
          status = 500;
          return response.status(status).json(apiResponse);
        }
        await trx.commit();
        apiResponse = {
          success: true,
          message: "Compte pharmacien créé avec succès",
          result: {
            pharmacien: pharmacien,
            user: user
          },
        }
        status = 201;
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        apiResponse = {
          success: false,
          message: "Une erreur serveur est survenue lors de la création du compte, veuillez réessayer plus tard",
          result: null,
          except: error.message,
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de l'enregistrement du personnel";
      apiResponse.success = false;
      apiResponse.except = error.message;

      return response.status(status).json(apiResponse);
    }
  }

  public async updatePersonal({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          id: schema.number(), // ID du pharmacien à mettre à jour
          last_name: schema.string(),
          first_name: schema.string(),
          email: schema.string.nullable({ trim: true }, [
            rules.email()
          ]),
          phone: schema.string(),
          country_id: schema.number(),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          gender: schema.enum(["M", "F"]),
          address: schema.string.optional(),
          profession: schema.string.optional(),
          role: schema.enum.nullable(['manager', 'pharmacist', 'caisse', 'comptable']),
        })
      });

      const { id, last_name, first_name, email, phone, country_id, birthday_year, birthday_month, birthday_day, gender, address, profession, role } = payload;

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        status = 401;
        apiResponse.message = "Veuillez vous authentifié pour continuer l'opération";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }

      const pharmacyId = personal.pharmacyId;
      const pharmacien = await Pharmacien.query().where('id', id).where('pharmacyId', pharmacyId).first();
      if (!pharmacien) {
        status = 404;
        apiResponse.message = "Le pharmacien est introuvable";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }

      // Mettre à jour les informations du pharmacien
      pharmacien.lastName = last_name;
      pharmacien.firstName = first_name;
      pharmacien.phone = phone;
      pharmacien.email = email;
      pharmacien.countryId = country_id;
      pharmacien.gender = gender;
      pharmacien.birthdayYear = birthday_year;
      pharmacien.birthdayMonth = birthday_month;
      pharmacien.birthdayDay = birthday_day;
      pharmacien.address = address ?? pharmacien.address;
      pharmacien.profession = profession ?? pharmacien.profession;
      pharmacien.role = role ?? pharmacien.role;

      await pharmacien.save();

      apiResponse = {
        success: true,
        message: "Informations du pharmacien mises à jour avec succès",
        result: pharmacien,
      };
      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la mise à jour des informations du pharmacien";
      apiResponse.success = false;
      apiResponse.except = error.message;

      return response.status(status).json(apiResponse);
    }
  }

  public async getEntityTransactions({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const entityId = await this.getPharmacyIdByPersonal(auth);
      if (entityId === null) {
        apiResponse.message = "Vous n'avez pas accès à cette fonctionnalité";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const walletEntity = await Wallet.query().where('owner_id', entityId).where('owner_type', 'pharmacy').andWhere('type_wallet_id', 3).first();
      if (walletEntity === null) {
        apiResponse.message = "Portefeuille de l'entité introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const transactions = await WalletHistory.query().where('wallet_id', walletEntity.id).orderBy('created_at', 'desc')
        .preload('transaction', function (query) {
          query.select(['beneficiary_id', 'paid_by_self', 'amount', 'payment_type_id', 'description', 'date_op', 'status', 'metadata', 'trx_ref', 'created_at', 'updated_at'])
          query.preload('beneficiary').preload('paymentType')
        })
        .preload('wallet')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Données récupérées",
        result: transactions,
      }
    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des transactions";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getLatestQuotationRequest({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;

    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;

      // Récupérer l'ID de la pharmacie associée à l'utilisateur authentifié
      const entityId = await this.getPharmacyIdByPersonal(auth);
      if (!entityId) {
        apiResponse = {
          success: false,
          message: "Vous n'avez pas accès à cette fonctionnalité",
          result: null,
        };
        status = 401;
        return response.status(status).json(apiResponse);
      }

      const quotationRequests = await QuotationRequest
        .query()
        .select('quotation_requests.*')
        .join('quotation_partners', 'quotation_requests.id', 'quotation_partners.quotation_request_id')
        .where('quotation_partners.pharmacy_id', entityId)
        .whereIn('quotation_partners.status', ['pending', 'partially_end'])
        // Correction pour MySQL (au lieu de INTERVAL '1 day')
        .where('quotation_partners.notified_at', '>=', Database.raw('DATE_SUB(NOW(), INTERVAL 1 DAY)'))
        .whereIn('quotation_requests.status', ['in_progress', 'partially_paid'])
        .whereNotExists((query) => {
          query.from('quotation_proposals as qp2')
            .whereRaw('qp2.quotation_request_id = quotation_requests.id')
            .whereRaw('qp2.version_id = quotation_requests.current_version')
            .andWhere('qp2.pharmacy_id', entityId);
        })
        .preload('patient')
        .preload('prescription', (query) => {
          query.preload('items', (itemQuery) => {
            itemQuery.preload('product').preload('substitutableProduct');
          });
        })
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des demandes de devis en attente pour ce partenaire (sans propositions)",
        result: quotationRequests,
      };

      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      console.error("Error in getLatestQuotationRequest:", error);
      apiResponse = {
        success: false,
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        result: null,
        except: error.message,
        errors: error.messages,
      };
      return response.status(status).json(apiResponse);
    }
  }


  public async getQuotationRequestNotResponded({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;

      const entityId = await this.getPharmacyIdByPersonal(auth);
      if (entityId === null) {
        apiResponse = {
          success: false,
          message: "Vous n'avez pas accès à cette fonctionnalité",
          result: null
        }
        status = 401;
        return response.status(status).json(apiResponse);
      }
      let quotationRequests = await QuotationRequest.query()
        .join('quotation_partners', 'quotation_partners.quotation_request_id', 'quotation_requests.id')
        .whereNotExists(function (query) {
          query.from('quotation_proposals')
            .where('quotation_proposals.pharmacy_id', entityId)
            .where('quotation_proposals.quotation_request_id', 'quotation_requests.id');
        })
        .where('quotation_partners.pharmacy_id', entityId)
        .preload('patient')
        .preload('prescription', function (query) {
          query.preload('items', function (query) {
            query.preload('product').preload('substitutableProduct');
          });
        })
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des demandes de devis en attente non traitées",
        result: quotationRequests
      }
      return response.status(status).json(apiResponse);

    } catch (error) {
      status = 500;
      console.log("error", error.message);
      apiResponse = {
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        success: false,
        result: null,
        except: error.message,
        errors: error.messages
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async getAnalyticalData({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;

    try {
      // Récupérer l'ID de la pharmacie associée à l'utilisateur authentifié
      const pharmacyId = await this.getPharmacyIdByPersonal(auth);

      if (pharmacyId === null) {
        apiResponse = {
          success: false,
          message: "Vous n'avez pas accès à cette fonctionnalité",
          result: null,
        };
        status = 401;
        return response.status(status).json(apiResponse);
      }

      // Effectuer les requêtes de comptage simultanément
      const [proposalAccepted, proposalRejected, quotationRequests, orders] = await Promise.all([
        Database.query().from('quotation_proposals')
          .where('pharmacy_id', pharmacyId)
          .where('status', 'approved')
          .count('* as total'),
        Database.query().from('quotation_partners')
          .where('pharmacy_id', pharmacyId)
          .whereIn('status', ['rejected', 'expired'])
          .count('* as total'),
        Database.query().from('quotation_partners')
          .where('pharmacy_id', pharmacyId)
          .whereIn('status', ['pending', 'accepted', 'completed'])
          .count('* as total'),
        Database.query().from('orders')
          .where('pharmacy_id', pharmacyId)
          .count('* as total'),
      ]);

      // Extraire les valeurs de comptage des résultats
      const totalProposalAccepted = parseInt(proposalAccepted[0].total || "0", 10);
      const totalProposalRejected = parseInt(proposalRejected[0].total || "0", 10);
      const totalQuotationRequest = parseInt(quotationRequests[0].total || "0", 10);
      const totalOrder = parseInt(orders[0].total || "0", 10);

      // Construire la réponse réussie
      apiResponse = {
        success: true,
        message: "Etat analytique",
        result: {
          totalProposalAccepted,
          totalProposalRefused: totalProposalRejected,
          totalQuotationRequest,
          totalOrder,
        },
      };
    } catch (error) {
      status = 500;
      console.error("Une erreur est survenue :", error);

      // Construire la réponse en cas d'erreur
      apiResponse = {
        success: false,
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        result: null,
      };
    }

    // Retourner la réponse finale
    return response.status(status).json(apiResponse);
  }

  public async updatePharmacyLocation({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          location: schema.object().members({
            latitude: schema.number(),
            longitude: schema.number(),
          })
        })
      });
      const { location } = payload;
      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        status = 401;
        apiResponse.message = "Veuillez vous authentifié pour continuer l'opération";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }
      const pharmacyId = personal.pharmacyId;
      const pharmacy = await Pharmacy.query().where('id', pharmacyId).first();
      if (!pharmacy) {
        status = 404;
        apiResponse.message = "Le compte est introuvable";
        apiResponse.success = false;
        return response.status(status).json(apiResponse);
      }

      await pharmacy.merge({
        location: JSON.stringify({ lat: location.latitude, long: location.longitude }),
      }).save();

      apiResponse = {
        success: true,
        message: "La position géographique de la pharmacie a été mise à jour avec succès",
        result: pharmacy,
      }
      return response.status(status).json(apiResponse);

    } catch (error) {
      console.log("error in update pharmacy", error);
      apiResponse = {
        success: false,
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        result: null,
        except: error.message,
        errors: error.messages
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }




}
