import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import PatientInsuranceCompany from './PatientInsuranceCompany'
import InsuranceCompanyAnalyzeAsk from './InsuranceCompanyAnalyzeAsk'
import AnalyzeAskItem from './AnalyzeAskItem'

export default class InsuranceCompanyAnalyzeAskItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'patient_company_assurance_id' })
  public patientCompanyAssuranceId: number

  @column({ columnName: 'insurance_company_analyze_ask_id' })
  public insuranceCompanyAnalyzeAskId: number

  @column({ columnName: 'analyze_ask_item_id' })
  public analyzeAskItemId: number

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column({ columnName: 'total_quantity_assured' })
  public totalQuantityAssured: number

  @column({ columnName: 'total_amount_assured' })
  public totalAmountAssured: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'validated' | 'blocked' | 'rejected'

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  // Relations
  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => PatientInsuranceCompany, {
    foreignKey: 'patientCompanyAssuranceId',
  })
  public patientCompanyAssurance: BelongsTo<typeof PatientInsuranceCompany>

  @belongsTo(() => InsuranceCompanyAnalyzeAsk, {
    foreignKey: 'insuranceCompanyAnalyzeAskId',
  })
  public insuranceCompanyAnalyzeAsk: BelongsTo<typeof InsuranceCompanyAnalyzeAsk>

  @belongsTo(() => AnalyzeAskItem, {
    foreignKey: 'analyzeAskItemId',
  })
  public analyzeAskItem: BelongsTo<typeof AnalyzeAskItem>
}
