import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import ProductType from './ProductType';

export default class CategoryProduct extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'name' })
  public name: string;

  @column({ columnName: 'description' })
  public description: string | null;

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => ProductType, {
    foreignKey: 'categoryProductId',
    localKey: 'id'
  })
  public productTypes: HasMany<typeof ProductType>
}
