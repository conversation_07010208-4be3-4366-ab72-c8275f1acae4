import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class InsuranceCompany extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public agrement: string

  @column()
  public email: string

  @column()
  public phone: string

  @column()
  public address: string

  @column()
  public seat: string

  @column()
  public countryId: number

  @column()
  public cityId: number

  @column()
  public website: string

  @column()
  public logo: string

  @column()
  public adhesionPrice: number

  @column()
  public taux: number

  @column({columnName: 'probation_duration'})
  public probationDuration: number

  @column()
  public status: string

  @column()
  public responsable: object

  @column()
  public configs: object

  @column()
  public certificat: object

  @column()
  public notes: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
