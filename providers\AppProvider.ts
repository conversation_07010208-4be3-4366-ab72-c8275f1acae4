import type { ApplicationContract } from '@ioc:Adonis/Core/Application'
// import NatService from 'App/Services/NatService'

export default class AppProvider {
  constructor (protected app: ApplicationContract) {
  }

  public register () {
    // Register your own bindings
  }

  public async boot () {
    // IoC container is ready
    // await NatService.getInstance()
  }

  public async ready() {
    if (this.app.environment === 'web') {
      await import('../start/socket')
    }
  }


  public async shutdown () {
    // Cleanup, since app is going down
  }
}
