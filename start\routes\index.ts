import Route from '@ioc:Adonis/Core/Route';
import './apis/auth';
import './apis/profile';
import './apis/commons';
import './apis/soignant';
import './apis/pharmacy';
import './apis/laboratory';
import User from 'App/Models/User';

Route.group(() => {
  Route.get('/', async () => {
    return { hello: "BIENVENUE SUR L'API HEALTHPRO DO" }
  });

  Route.post('/set-password',async({request})=>{
    const password = request.input('password');
    const userId = request.input('userId');

    const user = await User.query().where('id', userId).forUpdate().first();

    if (!user) {
      return {
        success: false,
        message: "User not found"
      }
    }

    user.password = password;
    await user.save();
    return {
      success: true,
      message: "Password set successfully",
      result: {
        password: password
      }
    }
  });
}).prefix('api')
