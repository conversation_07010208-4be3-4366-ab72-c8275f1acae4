import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Patient from './Patient'

export default class Wallet extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number | null

  @column({ columnName: 'owner_type' })
  public ownerType: string

  @column({ columnName: 'owner_id' })
  public ownerId: number

  @column({ columnName: 'libelle' })
  public libelle: string

  @column({ columnName: 'type_wallet_id' })
  public typeWalletId: number

  @column()
  public balance: number

  @column()
  public code: string

  @column()
  public currency: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User, {
    foreignKey: 'user_id',
    localKey: 'id'
  })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Patient, {
    foreignKey: 'ownerId',
    localKey: 'id',
  })
  public patient: BelongsTo<typeof Patient>;
}
