import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import Soignant from './Soignant'
import Pathology from './Pathology'
import CategoryDiagnostic from './CategoryDiagnostic'
import HealthInstitute from './HealthInstitute'
import Prescription from './Prescription'
import AnalyzeAsk from './AnalyzeAsk'

export default class Diagnostic extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'start_at' })
  public startAt: Date

  @column({ columnName: 'pro_id' })
  public proId: number | null

  @column({ columnName: 'fmd_id' })
  public fmdId: number | null

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number | null

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'libelle' })
  public libelle: string

  @column({ columnName: 'content' })
  public content: string

  @column({ columnName: 'symptoms',
    prepare: (value: any) => (value ? JSON.stringify(value) : null),
    // serialize: (value: any) => value ? JSON.parse(value) : null
  })
  public symptoms: any[]

  @column({ columnName: 'treatments' })
  public treatments: string[] | null

  @column({ columnName: 'category_diagnostic_id' })
  public categoryDiagnosticId: number

  @column({ columnName: 'pathology_id' })
  public pathologyId: number

  @column({ columnName: 'least_pathology_id' })
  public leastPathologyId: number

  @column({ columnName: 'other_pathologies',
    prepare: (value: any) => (value ? JSON.stringify(value) : null),
    // serialize: (value: any) => value ? JSON.parse(value) : null
  })
  public otherPathology: any

  @column({ columnName: 'examens' })
  public examens: string

  @column({ columnName: 'protocol_traitement_id' })
  public protocolTraitementId: number | null

  @column({ columnName: 'cat' })
  public cat: string | null

  @column({ columnName: 'consultation_price' })
  public consultationPrice: number

  @column({ columnName: 'diagnostic_final',
    prepare: (value: any) => (value ? JSON.stringify(value) : null),
    // serialize: (value: any) => value ? JSON.parse(value) : null
  })
  public diagnosticFinal: any

  @column({ columnName: 'files',
    prepare: (value: any) => (value ? JSON.stringify(value) : null),
    // serialize: (value: any) => value ? JSON.parse(value) : null
  })
  public files: string[] | null

  @belongsTo(() => Patient,{
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Soignant,{
    foreignKey: 'proId',
    localKey: 'id'
  })
  public pro: BelongsTo<typeof Soignant>

  @belongsTo(() => Pathology,{foreignKey: 'pathologyId',localKey: 'id'})
  public pathology: BelongsTo<typeof Pathology>

  @belongsTo(() => Pathology,{foreignKey: 'leastPathologyId',localKey: 'id'})
  public leastPathology: BelongsTo<typeof Pathology>

  @belongsTo(()=> CategoryDiagnostic,{foreignKey: 'categoryDiagnosticId',localKey: 'id'})
  public category: BelongsTo<typeof CategoryDiagnostic>

  @belongsTo(() =>HealthInstitute,{foreignKey: 'healthInstituteId',localKey: 'id'})
  public healthInstitute: BelongsTo<typeof HealthInstitute>

  @hasMany(() => Prescription,{
    foreignKey: 'diagnosticId',
    localKey: 'id'
  })
  public prescriptions: HasMany<typeof Prescription>

  @hasMany(()=> AnalyzeAsk,{
    foreignKey: 'diagnosticId',
    localKey: 'id'
  })
  public analyze_asks: HasMany<typeof AnalyzeAsk>


}
