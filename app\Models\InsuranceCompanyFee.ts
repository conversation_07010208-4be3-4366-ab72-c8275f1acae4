import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class InsuranceCompanyFee extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  // ... existing code ...
@column({ columnName: 'patient_id' })
public patient_id: number

@column({ columnName: 'patient_insurance_company_id' })
public patient_insurance_company_id: number

@column({ columnName: 'insurance_company_id' })
public insurance_company_id: number

@column({ columnName: 'insurance_company_agency_id' })
public insurance_company_agency_id: number

@column({ columnName: 'insurance_company_subscription_id' })
public insurance_company_subscription_id: number //ajouter l'année de souscription de l'assureur en cours

@column({ columnName: 'package_id' })
public package_id: number

@column({ columnName: 'price' })
public price: number

@column({ columnName: 'total_month' })
public total_month: number
// ... existing code ...
@column({ columnName: 'is_valide' })
public is_valide: boolean

@column({ columnName: 'paid_at' })
public paidAt: DateTime

@column({ columnName: 'start_at' })
public startAt: DateTime

@column({ columnName: 'end_at' })
public endAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
