import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Wallet from './Wallet'
import Transaction from './Transaction'

export default class WalletHistory extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'wallet_id' })
  public walletId: number

  @column({ columnName: 'transaction_id' })
  public transactionId: number

  @column()
  public amount: number

  @column({columnName: 'type'})
  public type: 'deposit' | 'payment' | 'transfer' | 'withdrawal'

  @column({ columnName: 'date_op' })
  public dateOp: DateTime

  @column()
  public description: string

  @column()
  public metadata: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Wallet)
  public wallet: BelongsTo<typeof Wallet>

  @belongsTo(() => Transaction,{foreignKey: 'transactionId',localKey: 'id'})
  public transaction: BelongsTo<typeof Transaction>
}
