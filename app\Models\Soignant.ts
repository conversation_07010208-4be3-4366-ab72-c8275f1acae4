import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import HealthInstitute from './HealthInstitute'
import User from './User'

export default class Soignant extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'address' })
  public address: string

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number

  @column({ columnName: 'quarter_id' })
  public quarterId: number

  @column({ columnName: 'gender' })
  public gender: string

  @column({ columnName: 'profession' })
  public profession: string

  @column({ columnName: 'birthday_year' })
  public birthdayYear: number

  @column({ columnName: 'birthday_month' })
  public birthdayMonth: number

  @column({ columnName: 'birthday_day' })
  public birthdayDay: number

  @column({ columnName: 'domain_id' })
  public domainId: number

  @column({ columnName: 'departement' })
  public departement: string

  @column({ columnName: 'docs',serializeAs: null })
  public docs: any

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'schedules' })
  public schedules: string

  @column({ columnName: 'code' })
  public code: string

  @column({columnName: 'is_anit'})
  public isAnit: boolean

  @column({ columnName: 'zones' })
  public zones: { city_id: number; district_id: number; address: string } | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(()=>User,{
    foreignKey: 'userId',
    localKey: 'id'
  })
  public user: BelongsTo<typeof User>

  @manyToMany(() => HealthInstitute, {
    pivotTable: 'personals',
    pivotForeignKey: 'soignant_id',
    pivotRelatedForeignKey: 'health_institute_id',
    pivotColumns: [
      'health_institute_id', 'soignant_id','is_active','is_principal','role',
      'departement','matricule','note','affiliation_date','end_date','created_at','updated_at'
    ],
  })
  public healthInstitutes: ManyToMany<typeof HealthInstitute>
}
