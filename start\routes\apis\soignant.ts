import Route from '@ioc:Adonis/Core/Route';
import ConsultationController from 'App/Controllers/Http/api/core/soignant/ConsultationController';
import HealthProController from 'App/Controllers/Http/api/core/soignant/HealthProController';
import PatientController from 'App/Controllers/Http/api/core/soignant/PatientController';

const ptCtrl = new PatientController();
const diagnosticCtrl = new ConsultationController();
const proCtrl = new HealthProController();

Route.group(() => {
  Route.group(() => {
    Route.get('/',async (ctx) => {
      return await ptCtrl.getPatientList(ctx);
    });
    Route.get('/actifs',async (ctx) => {
      return await ptCtrl.getPatientActifs(ctx);
    });
    Route.post('/create',async (ctx) => {
      return await ptCtrl.createPatientAccount(ctx);
    });
    Route.post('/scan-qrcode',async (ctx) => {
      return await ptCtrl.scanPatientQrcode(ctx);
    });
    Route.post('/add',async (ctx) => {
      return await ptCtrl.addPatientToHealthBook(ctx);
    });
    Route.get('/details',async (ctx) => {
      return await ptCtrl.getPatientDetails(ctx);
    });
    Route.group(() => {
      Route.post('/add',async (ctx) => {
        return await ptCtrl.addPatientHealthRecord(ctx);
      });
      Route.delete('/remove',async (ctx) => {
        return await ptCtrl.deletePatientHealthRecord(ctx);
      });
      Route.post('/update',async (ctx) => {
        return await ptCtrl.updatePatientHealthRecord(ctx);
      });
    }).prefix('health-records');

    Route.group(() => {
      Route.get('/vital-params',async (ctx) => {
        return await ptCtrl.getPatientVitalParams(ctx);
      });
      Route.post('/vital-params/add',async (ctx) => {
        return await ptCtrl.addPatientVitalParameter(ctx);
      });
      Route.get('/vital-parameters-with-format',async (ctx) => {
        return await ptCtrl.getPatientVitalParamsWithFormat(ctx);
      })
      Route.get('/consultations',async (ctx) => {
        return await ptCtrl.getPatientDiagnostics(ctx);
      });
      Route.get('/vital-params-by-exam',async (ctx) => {
        return await ptCtrl.getPatientvitalParamsByExam(ctx);
      });
      Route.get('/insurances',async (ctx) => {
        return await ptCtrl.getPatientInsurances(ctx);
      });
      Route.get('/insurances/packages',async (ctx) => {
        return await ptCtrl.getInsurancePackage(ctx);
      });
    })
  }).prefix('patients');



  Route.group(() => {
    Route.get('/',async (ctx) => {
      return await diagnosticCtrl.getDiagnostics(ctx);
    });
    Route.post('/create',async (ctx) => {
      return await diagnosticCtrl.createPatientDiagnostic(ctx);
    });
    Route.get('/details',async (ctx) => {
      return await diagnosticCtrl.getDiagnosticDetails(ctx);
    });
  }).prefix('diagnostics');
  Route.group(() => {
    Route.get('/',async (ctx) => {
      return await diagnosticCtrl.getPrescriptions(ctx);
    });
    Route.post('/create',async (ctx) => {
      return await diagnosticCtrl.addPrescriptionToDiagnostic(ctx);
    });
  }).prefix('prescriptions');
  Route.group(() => {
    Route.get('/',async (ctx) => {
      return await diagnosticCtrl.getAnalyzeAskByPro(ctx);
    });
    Route.post('/create',async (ctx) => {
      return await diagnosticCtrl.createDiagnosticAnalyzeAsk(ctx);
    });
  }).prefix('analyze-asks');

  Route.group(() => {
    Route.get('/affiliate',async (ctx) => {
      return await proCtrl.getHealthInstitutes(ctx);
    });
    Route.get('/pro',async (ctx) => {
      return await proCtrl.getProHealthInstitutes(ctx);
    });
  }).prefix('health-institutes');

  Route.group(() => {
    Route.get('/',async (ctx) => {
      return await proCtrl.getAppointments(ctx);
    });
  }).prefix('appointments');

}).prefix('api').namespace('App/Controllers/Http/api/core/soignant').middleware(['auth']);
