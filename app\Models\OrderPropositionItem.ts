import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class OrderPropositionItem extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  // Skip existing code...

  @column({ columnName: 'order_proposition_id' })
  public order_proposition_id: number

  @column({ columnName: 'order_item_id' })
  public order_item_id: number

  @column({ columnName: 'prescription_item_id' })
  public prescription_item_id: number

  @column({ columnName: 'product_id' })
  public product_id: number

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'image' })
  public image: string

  @column({ columnName: 'price' })
  public price: number

  @column({ columnName: 'assured_price' })
  public assured_price: number

  @column({ columnName: 'quantity' })
  public quantity: number

  @column({ columnName: 'choosed' })
  public choosed: boolean

  @column({ columnName: 'is_assured' })
  public is_assured: boolean

  @column({ columnName: 'is_paid' })
  public is_paid: boolean

  @column({ columnName: 'is_refund' })
  public is_refund: boolean

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime
}
