import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class VitalExam extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string // Nom de l'examen vital

  @column({ columnName: 'units' })
  public units: any[] | null // Unités de mesure

  @column({ columnName: 'description' })
  public description: string | null // Description de l'examen

  @column({ columnName: 'is_active' })
  public isActive: boolean // Indique si l'examen est actif

  @column({ columnName: 'normal_values' })
  public normalValues: { min: number, max: number } | null // Valeurs normales

  @column({ columnName: 'critical_values' })
  public criticalValues: { min: number, max: number } | null // Valeurs critiques

  @column({ columnName: 'configs' })
  public configs: any | null // Valeurs de configuration

  @column({ columnName: 'multiple_readings' })
  public multipleReading: boolean // Indique si il y a plusieurs lectures

  @column({ columnName: 'readings' })
  public readings: any[] | null // Lectures

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
