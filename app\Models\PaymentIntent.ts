import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class PaymentIntent extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'payment_type_id' })
  public paymentTypeId: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'beneficiary_id' })
  public beneficiaryId?: number

  @column({ columnName: 'currency' })
  public currency: string

  @column({ columnName: 'client' })
  public client?: any

  @column({ columnName: 'amount_paid' })
  public amountPaid: number

  @column({ columnName: 'fees' })
  public fees?: number

  @column({ columnName: 'payment_method' })
  public paymentMethod?: string

  @column({ columnName: 'phone' })
  public phone?: string

  @column({ columnName: 'network' })
  public network?: string

  @column({ columnName: 'metadata' })
  public metadata?: any

  @column({ columnName: 'payment_link' })
  public paymentLink?: string

  @column({ columnName: 'callback_url' })
  public callbackUrl?: string

  @column({ columnName: 'redirect_url' })
  public redirectUrl?: string

  @column({ columnName: 'qrcode_url' })
  public qrcodeUrl?: string

  @column({ columnName: 'status' })
  public status: 'pending' | 'paid' | 'cancelled' | 'failed'

  @column({ columnName: 'description' })
  public description?: string

  @column.dateTime({ columnName: 'request_at' })
  public requestAt?: DateTime

  @column.dateTime({ columnName: 'paid_at' })
  public paidAt?: DateTime

  @column.dateTime({ columnName: 'canceled_at' })
  public canceledAt?: DateTime

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime
}
