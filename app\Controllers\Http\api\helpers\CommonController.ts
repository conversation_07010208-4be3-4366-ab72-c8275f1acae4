import { string } from '@ioc:Adonis/Core/Helpers';
import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import HelperController from "./HelperController";
import { ApiResponse } from 'App/Controllers/Utils/models';
import { formatErrorResponse } from 'App/Controllers/Utils';
import Role from 'App/Models/Role';
import Country from 'App/Models/Country';
import BloodGroup from 'App/Models/BloodGroup';
import CategoryAnalyze from 'App/Models/CategoryAnalyze';
import CategoryDiagnostic from 'App/Models/CategoryDiagnostic';
import CategoryProduct from 'App/Models/CategoryProduct';
import City from 'App/Models/City';
import HealthInstitute from 'App/Models/HealthInstitute';
import Laboratory from 'App/Models/Laboratory';
import Language from 'App/Models/Language';
import PaymentGateway from 'App/Models/PaymentGateway';
import Pharmacy from 'App/Models/Pharmacy';
import Product from 'App/Models/Product';
import Quarter from 'App/Models/Quarter';
import TypeHealthInstitute from 'App/Models/TypeHealthInstitute';
import Database from '@ioc:Adonis/Lucid/Database';
import Wallet from 'App/Models/Wallet';
export default class CommonController extends HelperController {

  public async getproRoles({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const roles = await Role.query().whereIn('id', [3, 4, 5]);
      apiResponse = {
        success: true,
        message: "Roles",
        result: roles,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getCountries({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const countries = await Country.all();
      apiResponse = {
        success: true,
        message: "Countries",
        result: countries,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getCities({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const cities = await City.query().orderBy('name', 'asc').preload('country').preload('quarters');
      apiResponse = {
        success: true,
        message: "Cities",
        result: cities,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getCitiesByCountry({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {

      const payload = await request.validate({
        schema: schema.create({
          country_id: schema.string(),
        }),
      });

      const { country_id } = payload;
      const country = await Country.query().where('id', country_id).first();
      if (!country) {
        apiResponse = {
          success: false,
          message: "Country not found",
          result: null as any,
          except: null as any
        }
      } else {
        const cities = await City.query().where('country_id', country.id).orderBy('name', 'asc').preload('country').preload('quarters');
        apiResponse = {
          success: true,
          message: "Cities",
          result: cities,
        }
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getQuarters({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const quarters = await Quarter.query().orderBy('name', 'asc').preload('city', function (query) {
        query.preload('country');
      });
      apiResponse = {
        success: true,
        message: "Quarters",
        result: quarters,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getLanguages({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const languages = await Language.query().orderBy('name', 'asc');
      apiResponse = {
        success: true,
        message: "Languages",
        result: languages,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getPaymentGateways({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const gateways = await PaymentGateway.query().orderBy('name', 'asc');
      apiResponse = {
        success: true,
        message: "Payment Gateways",
        result: gateways,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getHealthInstitutes({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const healthInstitutes = await HealthInstitute.query().orderBy('name', 'asc')
        .preload('typeHealthInstitute').preload('country').preload('city').preload('quarter');

      apiResponse = {
        success: true,
        message: "Health Institutes",
        result: healthInstitutes,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getTypeHealthInstitutes({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const typeHealthInstitutes = await TypeHealthInstitute.query().orderBy('name', 'asc');
      apiResponse = {
        success: true,
        message: "Type Health Institutes",
        result: typeHealthInstitutes,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getBloodGroups({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const bloodGroups = await BloodGroup.query().orderBy('name', 'asc');
      apiResponse = {
        success: true,
        message: "Blood Groups",
        result: bloodGroups,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getPharmacies({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const pharmacies = await Pharmacy.query().orderBy('name', 'asc').preload('city');
      apiResponse = {
        success: true,
        message: "Pharmacies",
        result: pharmacies,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getLaboratoires({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const laboratoires = await Laboratory.query().orderBy('name', 'asc').preload('city');
      apiResponse = {
        success: true,
        message: "Laboratoires",
        result: laboratoires,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getCategoryProducts({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const categoryProducts = await CategoryProduct.query().orderBy('name', 'asc');
      apiResponse = {
        success: true,
        message: "Category Products",
        result: categoryProducts,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getCategoryAnalyzes({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const categoryAnalyzes = await CategoryAnalyze.query().orderBy('name', 'asc');
      apiResponse = {
        success: true,
        message: "Category Analyzes",
        result: categoryAnalyzes,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getCategoryDiagnostics({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const categoryDiagnostics = await CategoryDiagnostic.query().orderBy('name', 'asc');
      apiResponse = {
        success: true,
        message: "Category Diagnostics",
        result: categoryDiagnostics,
        except: null as any
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async getProductsByCategory({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {

      const payload = await request.validate({
        schema: schema.create({
          category_id: schema.string(),
        }),
      });

      const { category_id } = payload;
      const category = await CategoryProduct.query().where('id', category_id).preload('productTypes').first();
      if (!category) {
        apiResponse = {
          success: false,
          message: "Category not found",
          result: null as any,
          except: null as any
        }
      } else {
        const productTypes = category.productTypes;
        const products = await Product.query().where('product_type_id', productTypes.map(productType => productType.id))
          .orderBy('name', 'asc').preload('productType');

        apiResponse = {
          success: true,
          message: "Products",
          result: products,
          except: null as any
        }
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async generateWalletToEntity({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    };
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          entity_id: schema.number(),
          entity_type: schema.enum(['health_institute', 'pharmacy', 'laboratory']),
          libelle: schema.string.optional(),
        })
      });
      const { entity_id, entity_type,libelle } = payload;
      const trx = await Database.transaction();
      let walletCode = await this.generateWalletCode();
      const wallet = await Wallet.create({
        ownerType: entity_type,
        ownerId: entity_id,
        libelle: libelle ? libelle : "COMPTE ENTREPRISE",
        typeWalletId: 3,
        code: walletCode,
      }, { client: trx });

      if (!wallet) {
        await trx.rollback();
        apiResponse = {
          success: false,
          message: "Impossible de créer le compte",
          result: null,
          except: wallet
        }
        return response.status(500).json(apiResponse);
      }
      await trx.commit();
      apiResponse = {
        success: true,
        message: "Compte wallet crée avec succès",
        result: wallet,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error in wallet generate to entity", error.message);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async generateChannelToEntity({response}: HttpContextContract){
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status = 200;
    try {
      const entities = await Pharmacy.all();
      for(const entity of entities){
        if (entity.location !== null) {
          let channel = string.generateRandom(64);
          entity.channel = channel;
          await entity.save();
        }
      }
      apiResponse = {
        success: true,
        message: "Channels Generated",
        result: entities.length,
      }
    } catch (error) {
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }
}
