import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class CaAdhesionFee extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'package_id' })
  public packageId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'patient_insurance_company_id' })
  public patientInsuranceCompanyId: number

  @column({ columnName: 'price' })
  public price: number

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'insurance_company_subscription_id' })
  public insuranceCompanySubscriptionId: number

  @column({ columnName: 'is_valide' })
  public isValide: boolean

  @column.dateTime({ columnName: 'paid_at' })
  public paidAt: DateTime
}
