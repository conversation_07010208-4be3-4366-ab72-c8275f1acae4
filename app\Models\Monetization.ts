import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Monetization extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public userId: number

  @column()
  public code: string

  @column({columnName: 'type_piece'})
  public typePiece: 'CNI' | 'PASSEPORT' | 'PERMIS'

  @column({columnName: 'pieceData'})
  public pieceData: any | null

  @column({columnName: 'payment_method'})
  public paymentMethod: 'CARD' | 'CASH' | 'MOBILE' | 'WALLET'

  @column({columnName: 'paymentData'})
  public paymentData: any | null

  @column({columnName: 'balance'})
  public balance: any | null

  @column({columnName: 'previous_balance'})
  public previousBalance: any | null

  @column()
  public status: 'PENDING' | 'ACTIVE' | 'INACTIVE' | 'BLOCKED' | 'CANCELED'

  @column({columnName: 'previous_plan'})
  public previousPlan: any | null
}
