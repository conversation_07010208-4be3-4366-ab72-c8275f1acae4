
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Hash from '@ioc:Adonis/Core/Hash';
import { schema, rules } from '@ioc:Adonis/Core/Validator';
import { formatErrorResponse } from "App/Controllers/Utils";
import HelperController from "../helpers/HelperController";
import { DateTime } from 'luxon';
import { ApiResponse, KYCRequest, UserParrainage, UserStatus } from 'App/Controllers/Utils/models';
import User from 'App/Models/User';
import Soignant from 'App/Models/Soignant';
import Database from '@ioc:Adonis/Lucid/Database';
import Wallet from 'App/Models/Wallet';
import Mail from '@ioc:Adonis/Addons/Mail';
import Patient from 'App/Models/Patient';
import jwt from 'jsonwebtoken';
import ParrainageSetting from 'App/Models/ParrainageSetting';
import Monetization from 'App/Models/Monetization';
import Reversal from 'App/Models/Reversal';
import Identity from 'App/Models/Identity';
import Storage from 'App/Services/Storage';

export default class AuthController extends HelperController {

  public async generateJWT(user: User) {
    const secret = process.env.JWT_SECRET;
    if (!secret || secret === '') {
      throw new Error('JWT Secret is missing'); // Gérer l'absence de clé secrète
    }

    const payload = {
      user_id: user.id,
      role: user.roleId
    };

    const token = jwt.sign(payload, secret, {
      expiresIn: '7d',  // Durée de validité du token (30 jour)
      issuer: 'dokitaEyes', // Identifiant de l'émetteur
    });
    return token;
  }

  public async login({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          username: schema.string({ trim: true }),
          password: schema.string({ trim: true })
        }),
        messages: {
          'phone.required': "Le numero de téléphone est requis",
          'password.required': "Veuillez saisir votre mot de passe"
        }
      });
      const user = await User.query().where('phone', payload.username).orWhere('username', payload.username).orWhere('email', payload.username)
        .preload('role')
        .preload('country')
        .preload('language')
        .first();

      if (!user || user == null) {
        apiResponse = {
          success: false,
          message: "Identifiant ou mot de passe incorrect, veuillez réessayer",
          result: null as any,
          except: null as any
        }
        status = 404;
      } else {
        if (user.password == null) {
          apiResponse = {
            success: false,
            message: "Mot de passe incorrect, veuillez réessayer",
            result: null as any,
            except: "Password is null"
          }
          status = 404;
          return response.status(status).json(apiResponse);
        }
        const checkPwd = await Hash.verify(String(user.password), payload.password);
        if (checkPwd) {
          //save user histories connection and update user is online
          user.online = 1;
          await user.save();
          const new_connection = await user.related('userHistories').create({
            userId: user.id,
            connectedAt: DateTime.now(),
            ipAddress: request.ip(),
            userAgent: request.header("User-Agent") || '',
            deviceType: request.header("User-Agent")?.includes("Mobile") ? "Mobile" : "Desktop",
            type: "login",
          });
          const token = await auth.use('api').generate(user, {
            expiresIn: '7day',
            name: user.username,
          });
          const token_jwt = await this.generateJWT(user);
          const pro = await this.getEntityPersonalByRoleId(user.roleId, user.id);
          const wallet = await Wallet.query().where('user_id', user.id).where('owner_type', 'soignant').where('owner_id', Number(pro?.id)).first();

          let userParrainage: UserParrainage = user.parrainage;
          let metadata = {} as any;
          if (userParrainage) {
            const plan = await ParrainageSetting.query().where('id', userParrainage.plan).first();
            const monetization = await Monetization.query().where('user_id', user.id).first();
            const lastReversal = await Reversal.query().where('user_id', user.id).orderBy('created_at', 'desc').first();
            metadata = {
              plan: plan,
              parrainage: userParrainage,
              monetization: monetization,
              lastReversal: lastReversal
            }
          }
          let jwt_expire = DateTime.now().plus({ days: 7 });

          apiResponse = {
            success: true,
            message: "Connexion reussie",
            result: {
              user: user,
              token: token,
              jwt: token_jwt,
              expired_at: jwt_expire,
              connection: new_connection,
              entity: pro,
              metadata,
              wallet,
            },
          }
          status = 200;
        } else {
          apiResponse = {
            success: false,
            message: "Identifiant ou mot de passe incorrect, veuillez réessayer",
            result: null as any,
            except: null as any
          }
          status = 404;
        }
      }
    } catch (error) {
      console.log("api error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message,
        errors: error.messages
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async logout({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 200;
    try {
      const authUser = await auth.authenticate();
      authUser.online = 0;
      await authUser.save();
      const newLogout = await authUser.related('userHistories').create({
        userId: authUser.id,
        connectedAt: DateTime.now(),
        ipAddress: request.ip(),
        userAgent: request.header("User-Agent") || '',
        deviceType: request.header("User-Agent")?.includes("Mobile") ? "Mobile" : "Desktop",
        type: "logout",
      });
      await auth.logout();
      apiResponse = {
        success: true,
        message: "Deconnexion reussie",
        result: newLogout,
        except: null as any
      }
      status = 200;
    } catch (error) {
      console.log("api error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async register({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 200;
    const trx = await Database.transaction();
    try {
      const payload = await request.validate({
        schema: schema.create({
          last_name: schema.string({ trim: true }),
          first_name: schema.string({ trim: true }),
          email: schema.string.optional({ trim: true }, [
            rules.email()
          ]),
          phone: schema.string({ trim: true }),
          password: schema.string({ trim: true }),
          gender: schema.enum.optional(['M', 'F']),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          country_id: schema.number(),
          profession: schema.string.optional(),
        }),
        messages: {
          'last_name.required': "Le nom d'utilisateur est requis",
          'email.required': "L'email est requis",
          'phone.required': "Le numéro de téléphone est requis",
          'password.required': "Veuillez saisir votre mot de passe",
          'gender.required': "Veuillez sélectionner votre genre",
          'country_id.required': "Veuillez sélectionner votre pays",
          'birthday_year.required': "Veuillez sélectionner votre année de naissance",
          'birthday_month.required': "Veuillez sélectionner votre mois de naissance",
          'birthday_day.required': "Veuillez sélectionner votre jour de naissance",
        }
      });

      const {
        last_name, first_name, email, phone, password, gender, birthday_year, birthday_month,
        birthday_day, country_id, profession,
      } = payload;

      let username = `${last_name} ${first_name}`;
      const check_exist_user = await User.query().where('phone', phone).orWhere('email', String(email)).first();

      if (check_exist_user) {
        apiResponse = {
          success: false,
          message: "Echec, le compte existe déjà avec cet email ou ce numéro de téléphone",
          result: null as any,
          except: null as any
        }
        status = 404;
        return response.status(status).json(apiResponse);
      } else {
        let codeP = await this.generateCodeParrainage(8);
        const parrainage = {
          create_account: 0,
          active_qrcode: 0,
          adhesion_fees: 0,
          plan: 1,
          activeMoney: false
        }

        const user = await User.create({
          username: username,
          phone: phone,
          email: email,
          password: password,
          countryId: country_id,
          roleId: 3,
          status: UserStatus.Pending,
          codeParrainage: codeP,
          parrainage: JSON.stringify(parrainage),
        }, { client: trx });

        let account;
        const code = await this.generateToken();

        // Création du compte soignant
        const soignant = await Soignant.create({
          userId: user.id,
          lastName: last_name,
          firstName: first_name,
          phone: phone,
          email: email,
          countryId: country_id,
          gender: gender,
          birthdayYear: birthday_year,
          birthdayMonth: birthday_month,
          birthdayDay: birthday_day,
          code: code,
          profession: profession,
          status: 'pending'
        }, { client: trx });

        if (soignant) {
          let walletCode = await this.generateWalletCode();
          await Wallet.create({
            userId: user.id,
            ownerType: "soignant",
            ownerId: soignant.id,
            libelle: "DO PRO WALLET",
            typeWalletId: 2,
            code: walletCode,
          }, { client: trx });

          await trx.commit();

          apiResponse = {
            success: true,
            message: "Bravo! Votre compte a été créé avec succès",
            result: {
              account: account,
              user: user
            },
            except: null as any
          }
          status = 201;
        } else {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Une erreur est survenue lors de la création de votre compte DO PRO",
            result: null as any,
            except: account
          }
          status = 404;
        }
      }
    } catch (error) {
      await trx.rollback();
      console.log("error in create pro account", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: null as any
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async sendCodeToPhoneNumber({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status = 200;
    try {
      const phone = request.input('phone');
      const authUser = await auth.authenticate();
      const user = await User.query().where('phone', phone).preload('country').first();
      // console.log("user",user);

      if (user && authUser && user.id == authUser.id) {
        let token = Math.floor(Math.random() * 900000) + 100000;
        let message = "Votre code de verification est : " + token;
        let country = user.country;
        if (country) {
          let phoneNumber = '+' + country.prefix + phone;
          console.log("phone number", phoneNumber);

          const sms = await this.sendSMS(phoneNumber, message);
          if (sms.success) {
            status = 200;
            await user?.merge({ token: token.toString() }).save();
            apiResponse = {
              success: true,
              message: "Code envoyé à votre numéro",
              result: sms.result,
              except: null as any
            }
          } else {
            apiResponse = {
              success: false,
              message: "Une erreur est survenue lors de l'envoi du SMS",
              result: null as any,
              except: sms
            }
          }
        } else {
          apiResponse = {
            success: false,
            message: "Aucun pays ne correspond à votre numéro",
            result: null as any,
            except: country
          }
        }
      } else {
        status = 404;
        apiResponse = {
          success: false,
          message: "Echec, votre numero est incorrect, veuillez réessayez avec un numero correct",
          result: null as any,
          except: user
        }
      }
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async verifyPhoneNumber({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          code: schema.string(),
        }),
      });
      const { code } = payload;
      const user = await User.findBy('token', code);
      const authUser = await auth.authenticate();
      if (user?.id === authUser?.id) {
        if (user.token === code) {
          status = 200;
          await user?.merge({
            token: null,
            phoneVerifiedAt: DateTime.local(),
            activatedAt: DateTime.local(),
          }).save();
          apiResponse = {
            success: true,
            message: "Félicitation, votre compte a bien été verifié",
            result: user,
          }
        } else {
          status = 404;
          apiResponse = {
            success: false,
            message: "Code invalide",
            result: null as any,
            except: user
          }
        }
      } else {
        status = 404;
        apiResponse = {
          success: false,
          message: "Code invalide",
          result: null as any,
          except: user
        }
      }
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async changePassword({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: ""
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          old_password: schema.string({ trim: true }),
          password: schema.string({ trim: true }),
          password_confirmation: schema.string({ trim: true }, [
            rules.confirmed('password')
          ]),
        })
      });
      const authUser = await auth.authenticate();
      if (authUser) {
        const user = await User.findOrFail(authUser.id);
        if (payload.old_password && await Hash.verify(String(user?.password), payload.old_password)) {
          user.password = payload.password;
          user.save();
          apiResponse = {
            success: true,
            message: "Mot de passe mis à jour avec succès",
            result: authUser,
            except: null as any
          }
        } else {
          apiResponse = {
            success: false,
            message: "Mot de passe incorrect",
            result: null as any,
            except: null as any
          }
        }
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async forgotPassword({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          email: schema.string({ trim: true }, [
            rules.email()
          ]),
        }),
      });
      const { email } = payload;
      const user = await User.query().where('email', email).first();
      if (user !== null) {
        let token = Math.floor(Math.random() * 900000) + 100000;

        const pwd_reset = await Database.table('password_reset_tokens').insert({
          email: payload.email,
          token: token,
          created_at: new Date(),
        });
        if (pwd_reset !== null) {
          //Send email
          let subject = "Réinitialisation de mot de passe";
          let is_send = false;
          let email = user.email;
          const sendMail = await Mail.send((message) => {
            message
              .from(String(process.env.SMTP_USERNAME))
              .to(String(email))
              .subject(subject)
              .htmlView('emails/forgot_password', {
                data: {
                  name: user.username,
                  code: token
                }
              });
          });
          if (sendMail) {
            is_send = true;
          }
          apiResponse = {
            success: true,
            message: "Un email a été envoyé à votre adresse mail",
            result: {
              email: email,
              token: token,
              mail_send: is_send
            },
          }
        }
      }
    } catch (error) {
      console.log("error", error);
      status = 500;
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(status).json(apiResponse);
  }

  /**
   * Reset password function
   * @param request
   * @param response
   * @returns
   */
  public async reset_password({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null
    }
    try {
      const userSchema = schema.create({
        code: schema.string(),
        password: schema.string(),
        confirm_password: schema.string()
      });

      const payload = await request.validate({
        schema: userSchema,
        messages: {
          required: 'Le champ {{ field }} est obligatoire',
        }
      });

      if (payload.password !== payload.confirm_password) {
        apiResponse = {
          success: false,
          message: "Les mots de passe ne sont pas identiques",
          result: null
        }
      } else {
        const pwd_reset = await Database
          .from('password_reset_tokens')
          .where('token', payload.code)
          .first();

        if (pwd_reset !== null) {
          const user = await User.findOrFail(pwd_reset.user_id);
          if (user !== null) {
            user.password = payload.password;
            await user.save();
            //update password reset token
            await Database.from('password_reset_tokens').where('token', pwd_reset.token).update({
              token: null,
            });
            //Send email
            let subject = "Modification du mot de passe";
            let is_send = false;
            let email = user.email;
            const sendMail = await Mail.send((message) => {
              message
                .from(String(process.env.SMTP_USERNAME))
                .to(String(email))
                .subject(subject)
                .htmlView('emails/reset-password', {
                  data: {
                    name: user.username
                  }
                });
            });
            if (sendMail) {
              is_send = true;
            }
            apiResponse = {
              success: true,
              message: "Mot de passe mis à jour avec succès",
              result: {
                user: user,
                send_mail: is_send
              }
            }
          }
        } else {
          apiResponse = {
            success: false,
            message: "Code invalide ou expiré",
            result: null
          }
        }
      }
    } catch (error) {
      console.log("error in password reset", error.message);

      apiResponse = {
        success: false,
        message: "Une erreur est survenue",
        result: null,
        errors: error.messages?.errors,
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async createPatientAccount({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 201;
    const trx = await Database.transaction();
    try {
      const payload = await request.validate({
        schema: schema.create({
          last_name: schema.string({ trim: true }),
          first_name: schema.string({ trim: true }),
          email: schema.string.optional({ trim: true }, [
            rules.email()
          ]),
          phone: schema.string.optional({ trim: true }),
          password: schema.string.optional({ trim: true }),
          gender: schema.enum.optional(['M', 'F']),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          blood_group_id: schema.number.optional(),
          is_rural: schema.boolean.optional(),
          is_isme: schema.boolean.optional(),
          country_id: schema.number(),
          hasPhone: schema.boolean.optional(), // ce patient a-t-il un numéro de téléphone
        }),
        messages: {
          'last_name.required': "Le nom d'utilisateur est requis",
          'email.required': "L'email est requis",
          'phone.required': "Le numero de numéro est requis",
          'password.required': "Veuillez saisir votre mot de passe",
          'gender.required': "Veuillez selectionner votre genre",
          'country_id.required': "Veuillez selectionner votre pays",
          'blood_group_id.required': "Veuillez selectionner votre groupe sanguin",
          'birthday_year.required': "Veuillez selectionner votre annee de naissance",
          'birthday_month.required': "Veuillez selectionner votre mois de naissance",
          'birthday_day.required': "Veuillez selectionner votre jour de naissance",
        }
      });

      const {
        last_name, first_name, email, phone, password, gender, birthday_year, birthday_month,
        birthday_day, blood_group_id, is_rural, country_id, is_isme, hasPhone,
      } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse = {
          success: false,
          message: "Veuillez vous connecter avant de créer un compte",
          result: null as any,
          except: null as any
        }
        status = 401;
        return response.status(status).json(apiResponse);
      }
      let username = `${last_name} ${first_name}`;
      let check_exist_user: User | null = null;
      if (hasPhone && phone) {
        check_exist_user = await User.query().where('phone', phone).orWhere('email', String(email)).first();
      }

      if (check_exist_user !== null) {
        apiResponse = {
          success: false,
          message: "Cet Patient existe déjà",
          result: null as any,
          except: null as any
        }
        status = 404;
        return response.status(status).json(apiResponse);
      } else {
        const roleId = await this.getPatientRoleId();
        let codeP = await this.generateCodeParrainage(8);

        const codeParrainage = {
          create_account: 0,
          active_qrcode: 0,
          adhesion_fees: 0,
          plan: 1,
          activeMoney: false
        }

        const user = await User.create({
          username: username,
          phone: phone,
          email: email,
          password: hasPhone ? password : null,
          countryId: country_id,
          roleId: roleId,
          status: UserStatus.Actived,
          codeParrainage: codeP,
          parrainage: JSON.stringify(codeParrainage),
          creatorId: authUser.id,
        }, { client: trx });

        if (user && user.id) {
          //create code parrainage
          //create patient
          let codePatient = await this.generateToken();
          const patient = await Patient.create({
            user_id: user.id,
            last_name: last_name,
            first_name: first_name,
            email: email,
            phone: phone,
            gender: gender,
            birthday_year: birthday_year,
            birthday_month: birthday_month,
            birthday_day: birthday_day,
            blood_group_id: blood_group_id,
            isRural: is_rural !== undefined ? is_rural : false,
            is_isme: is_isme !== undefined ? is_isme : false,
            country_id: country_id,
            status: 'activated',
            code: codePatient.toString(),
          }, { client: trx });

          if (patient && patient.id) {
            //create wallet
            let walletCode = await this.generateWalletCode();
            const wallet = await Wallet.create({
              userId: user.id,
              ownerType: 'patient',
              ownerId: patient.id,
              libelle: "DO WALLET",
              typeWalletId: 2,
              code: walletCode,
            }, { client: trx });
            await trx.commit();

            apiResponse = {
              success: true,
              message: "Création de compte patient réussie avec succès",
              result: {
                user: user,
                codeParrainage: codeParrainage,
                patient: patient,
                wallet: wallet
              },
              except: null as any
            }
            status = 201;
          } else {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Une erreur est survenue lors de la creation de votre compte patient",
              result: null as any,
              except: patient
            }
            status = 500;
          }
        }
      }
    } catch (error) {
      console.log("error in create pro account", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: null as any
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  // Vérification de l'existence d'un compte
  public async checkUserPhoneLogin({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 200;

    try {
      // Validation des données de la requête
      const payload = await request.validate({
        schema: schema.create({
          phone: schema.string({ trim: true }),
        }),
        messages: {
          'phone.required': "Le numéro de téléphone est requis",
        },
      });

      const { phone } = payload;
      const user = await User.query()
        .where('phone', phone)
        .orWhere('identification_number', phone)
        .first();

      if (user) {
        const isOld = user.isOld;
        const hasIdentificationNumber = !!user.identificationNumber;

        apiResponse = {
          success: true,
          message: "Compte vérifié",
          result: {
            is_old: isOld ? true : false,
            status: user.status,
            is_phone_suffix: hasIdentificationNumber,
            is_sponsored: hasIdentificationNumber && isOld,
          },
        };
      } else {
        apiResponse = {
          success: false,
          message: "Numero de téléphone ou d'identification incorrecte, veuillez saisir un numero correcte !",
          result: null,
        };
        status = 404;
      }
    } catch (error) {
      console.log("Erreur API :", error.message);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message,
      };
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async saveUserIdentity({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          first_name: schema.string(),
          last_name: schema.string(),
          email: schema.string.optional(),
          phone: schema.string.optional(),
          contact: schema.string(),
          gender: schema.enum(['M', 'F']),
          address: schema.string.optional(),
          attestation_img: schema.file.optional({
            size: '12mb',
            extnames: ['jpg', 'png', 'jpeg', 'pdf'],
          }),
          cni_img: schema.file.optional({
            size: '12mb',
            extnames: ['jpg', 'png', 'jpeg', 'pdf'],
          }),
        })
      });
      const { first_name, last_name, email, phone, contact, gender, address, cni_img, attestation_img } = payload;
      let userId: number | null = null;
      let ownerId: number | null = null;

      const checkSoignant = await Soignant.query().where('last_name', last_name).where('first_name', first_name).orWhere('phone', String(phone)).preload('user').first();
      if (!checkSoignant) {
        apiResponse = {
          success: false,
          message: "Aucun compte soignant n'existe avec ce numéro, veuillez réessayer plus tard",
          result: null
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      userId = checkSoignant.userId;
      ownerId = checkSoignant.id;
      const user = checkSoignant.user;
      let bucketName = 'soignants';
      const storage = new Storage();
      // Créer l'objet KYCRequest avec les nouvelles valeurs
      let updatedDocs: Partial<KYCRequest> = {};

      // Gérer l'image de l'attestation
      if (attestation_img) {
        let fileName = `${Date.now()}-${attestation_img.clientName}`;
        let fileExt = attestation_img.extname;
        const objectName = `/confirmations/${fileName}`;
        const filePath = String(attestation_img.tmpPath);
        let metadata = {
          'Content-Type': attestation_img.type,
          'Content-Disposition': `attachment; filename=${fileName}`,
          size: attestation_img.size,
          extension: attestation_img.extname,
        }
        await storage.uploadFile(bucketName, objectName, filePath, metadata);
        updatedDocs.attestation_img = `${fileName}.${fileExt}`;
      }

      // Gérer l'image de la CNI
      if (cni_img) {
        let fileName = `${Date.now()}-${cni_img.clientName}`;
        let fileExt = cni_img.extname;
        const objectName = `/confirmations/${fileName}`;
        const filePath = String(cni_img.tmpPath);
        let metadata = {
          'Content-Type': cni_img.type,
          'Content-Disposition': `attachment; filename=${fileName}`,
          size: cni_img.size,
          extension: cni_img.extname,
        }
        await storage.uploadFile(bucketName, objectName, filePath, metadata);
        updatedDocs.cni_img = `${fileName}.${fileExt}`;
      }

      const identity = await Identity.create({
        userId: userId,
        ownerId: ownerId,
        ownerType: 'soignant',
        firstName: first_name,
        lastName: last_name,
        email: email !== null ? email : null,
        phone: phone !== null ? phone : null,
        gender: gender !== null ? gender : null as any,
        address: address,
        contact: contact,
        status: 'kyc',
        docs: JSON.stringify(updatedDocs)
      });
      if (!identity) {
        apiResponse = {
          success: false,
          message: "Identity not created",
          result: null as any,
          except: null as any
        }
        status = 400;
        return response.status(status).json(apiResponse);
      }
      await checkSoignant.merge({
        status: 'kyc'
      }).save();
      await user.merge({
        status: UserStatus.SendIdentity
      }).save();
      apiResponse = {
        success: true,
        message: "Identity created",
        result: identity,
      }
      status = 201;
      return response.status(status).json(apiResponse);

    } catch (error) {
      status = 500;
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message,
      };
      return response.status(status).json(apiResponse);
    }
  }

  public async checkUserIdentity({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 200;
    try {
      const identityId = request.input('identity_id');
      if (!identityId) {
        apiResponse.message = "Veuillez fournir l'id de l'identité";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const identity = await Identity.query().where('id', identityId).first();
      if (!identity) {
        apiResponse.message = "L'identité n'existe pas";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Identité retrouvé",
        result: identity,
      }
    } catch (error) {
      console.log("error occured");
      status = 500;
      apiResponse = {
        success: false,
        message: "Une erreur est survenue",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async resetOldUserPassword({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          phone: schema.string({ trim: true }),
          password: schema.string({ trim: true }),
          confirm_password: schema.string({ trim: true }),
        }),
        messages: {
          'phone.required': "Le numéro de téléphone est requis",
          'password.required': "Veuillez saisir votre mot de passe",
          'confirm_password.required': "Veuillez confirmer votre mot de passe"
        }
      });

      const { phone, password, confirm_password } = payload;
      const user = await User.query().where('phone', phone).orWhere('identification_number', phone).first();

      if (password !== confirm_password) {
        apiResponse = {
          success: false,
          message: "Les mots de passe ne correspondent pas",
          result: null as any,
          except: null as any
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }

      if (!user) {
        apiResponse = {
          success: false,
          message: "Cet utilisateur n'existe pas",
          result: null as any,
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        await user.merge({
          password: password,
          status: UserStatus.Actived,
          activatedAt: DateTime.now(),
        }).useTransaction(trx).save();
        const wallet = await Wallet.query().where('userId', user.id).where('owner_type','soignant').first();
        if (!wallet) {
          const newWallet = await Wallet.create({
            userId: user.id,
            ownerType: 'soignant',
            ownerId: user.id,
            libelle: "DO PRO WALLET",
            typeWalletId: 2,
            code: await this.generateWalletCode(),
          }, { client: trx });
          if (!newWallet) {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Une erreur est survenue lors de la création du wallet",
              result: null as any,
              except: newWallet
            }
          }
        }
        await trx.commit();
        apiResponse = {
          success: true,
          message: "Mot de passe modifié avec succès",
          result: user,
        }
        status = 200;
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error", error);
        apiResponse = {
          success: false,
          message: formatErrorResponse(error),
          result: null as any,
          except: error.message
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error.message
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

}
