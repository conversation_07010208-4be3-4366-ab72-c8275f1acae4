import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { ResultType } from 'App/Controllers/Utils/models'

export default class AnalyzeAskResultItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'analyze_ask_result_id' })
  public analyzeAskResultId: number

  @column({ columnName: 'analyze_ask_item_id' })
  public analyzeAskItemId: number

  @column({ columnName: 'result_type' })
  public resultType: ResultType

  @column({ columnName: 'result_value' })
  public resultValue: number | null

  @column({ columnName: 'result_text' })
  public resultText: string | null

  @column({ columnName: 'result_boolean' })
  public resultBoolean: boolean | null

  @column({ columnName: 'result_category' })
  public resultCategory: string | null

  @column({ columnName: 'result_media_path' })
  public resultMediaPath: string | null

  @column({ columnName: 'unity_id' })
  public unityId: number | null

  @column({ columnName: 'reference_min' })
  public referenceMin: string | null

  @column({ columnName: 'reference_max' })
  public referenceMax: string | null

  @column({ columnName: 'is_within_range' })
  public isWithinRange: boolean | null

  @column({ columnName: 'is_view' })
  public isView: boolean

  @column({ columnName: 'comment' })
  public comment: string | null

  @column({ columnName: 'metadata' })
  public metadata: any | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
