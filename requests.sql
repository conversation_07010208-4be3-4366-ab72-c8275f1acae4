SELECT
    analyze_asks.*,
    orders.total_price AS order_total_price,
    orders.status AS order_status,
    patients.last_name,
    patients.first_name,
    diagnostics.*,
    quotation_proposals.total_price AS proposal_price,
    orders.id AS order_id
FROM
    analyze_asks
    INNER JOIN quotation_requests ON analyze_asks.id = quotation_requests.analyze_ask_id
    INNER JOIN orders ON orders.quotation_request_id = quotation_requests.id
    AND orders.laboratory_id = 1
    AND orders.status = 'paid'
    AND orders.analyze_ask_id IS NOT NULL
    INNER JOIN quotation_proposals ON quotation_proposals.quotation_request_id = quotation_requests.id
    AND quotation_proposals.laboratory_id = 1
    AND quotation_proposals.status = 'approved'
    INNER JOIN patients ON analyze_asks.patient_id = patients.id
    INNER JOIN diagnostics ON analyze_asks.diagnostic_id = diagnostics.id
WHERE
    analyze_asks.result_is_defined = 0
    AND analyze_asks.is_requested = 1
    AND analyze_asks.payment_status IN ('paid', 'partially_paid')
ORDER BY
    analyze_asks.created_at DESC;