import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Service extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column({columnName: 'type_appointment'})
  public typeAppointment: 'hospital' | 'fmd' | 'home'

  @column({columnName: 'category'})
  public category: 'consultation' | 'exam' | 'treatment' | 'visite'

  @column()
  public role: 'doctor' | 'ide' | 'facilitator'

  @column({columnName: 'is_paid'})
  public isPaid: boolean

  @column()
  public price: number

  @column()
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
