import Route from '@ioc:Adonis/Core/Route';
import AuthController from 'App/Controllers/Http/api/auth/AuthController';
import ProfileController from 'App/Controllers/Http/api/auth/ProfileController';

const authCtrl = new AuthController();
const profilCtrl = new ProfileController();
Route.group(()=>{
  Route.group(()=>{

    Route.post('/check-phone',async(ctx) => {
      return await authCtrl.checkUserPhoneLogin(ctx);
    });
    Route.post('/confirm-identity', async(ctx) => {
      return await authCtrl.saveUserIdentity(ctx);
    });
    Route.get('/check-identity', async(ctx) => {
      return await authCtrl.checkUserIdentity(ctx);
    });
    Route.post('/reset-old-user-password', async (ctx) => {
      return await authCtrl.resetOldUserPassword(ctx);
    });

    Route.post('/login',async(ctx) => {
      return await authCtrl.login(ctx);
    });
    Route.post('/register',async(ctx) => {
      return await authCtrl.register(ctx);
    });
    Route.post('/forgot-password',async(ctx) => {
      return await authCtrl.forgotPassword(ctx);
    });
    Route.post('/reset-password',async(ctx) => {
      return await authCtrl.reset_password(ctx);
    });
    Route.group(() => {
      Route.post('/send-code',async(ctx) => {
        return await authCtrl.sendCodeToPhoneNumber(ctx);
      });

      Route.post('/verify-code',async(ctx) => {
        return await authCtrl.verifyPhoneNumber(ctx);
      });
      Route.post('/logout',async(ctx) => {
        return await authCtrl.logout(ctx);
      });

      Route.post('/update-password',async(ctx) => {
        return await authCtrl.changePassword(ctx);
      });
      Route.post('/update-account',async(ctx) => {
        return await profilCtrl.updateUserLocation(ctx);
      });
      Route.post('/update-user',async(ctx) => {
        return await profilCtrl.updateUserProfil(ctx);
      });
      Route.post('/account/send-kyc',async(ctx) => {
        return await profilCtrl.sendRequestAccountKYC(ctx);
      });
      Route.get('/profile',async(ctx) => {
        return await profilCtrl.getProfile(ctx);
      });
    }).middleware('auth')

  }).prefix('auth').namespace('App/Controllers/Http/api/auth');
}).prefix('api')
