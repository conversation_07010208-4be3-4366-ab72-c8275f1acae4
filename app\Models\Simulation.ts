import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Simulation extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'simulable_id' })
  public simulable_id: number

  @column({ columnName: 'simulable_type' })
  public simulable_type: string

  @column({ columnName: 'patient_id' })
  public patient_id: number

  @column({ columnName: 'duration' })
  public duration: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
