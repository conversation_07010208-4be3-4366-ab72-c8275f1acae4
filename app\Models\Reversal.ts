import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Monetization from './Monetization'

export default class Reversal extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({columnName: 'user_id'})
  public userId: number

  @column({columnName: 'monetization_id'})
  public monetizationId: number

  @column({columnName: 'reference'})
  public reference: string | null

  @column()
  public type: 'CASH' | 'WALLET' | 'CARD' | 'MOBILE'

  @column()
  public amount: number

  @column()
  public status: 'PENDING' | 'PAID' | 'CANCELED'

  @column({columnName: 'trx_reference'})
  public trxReference: string | null

  @column()
  public metadata: Record<string, any> | null

  @column({columnName: 'request_at'})
  public requestAt: Date | null

  @column({columnName: 'paid_at'})
  public paidAt: Date | null

  @belongsTo(()=>Monetization,{
    foreignKey: 'monetizationId',
    localKey: 'id'
  })
  public monetization: BelongsTo<typeof Monetization>
}
