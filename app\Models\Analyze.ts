import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, HasMany, ManyToMany, belongsTo, column, hasMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import CategoryAnalyze from './CategoryAnalyze'
import InsuranceCompanyAnalyze from './InsuranceCompanyAnalyze'
import AnalyzeAskItem from './AnalyzeAskItem'

export default class Analyze extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'eng_name' })
  public eng_name: string

  @column({ columnName: 'price' })
  public price: number

  @column({ columnName: 'type' })
  public type: string

  @column({ columnName: 'parent_id' })
  public parent_id: number

  @column({ columnName: 'category_analyze_id' })
  public category_analyze_id: number

  @column({ columnName: 'serie' })
  public serie: string

  @column({ columnName: 'unities' })
  public unities: string[]

  @column({ columnName: 'is_pack' })
  public is_pack: boolean

  @column({ columnName: 'is_pm' })
  public is_pm: boolean

  @column({ columnName: 'is_payable' })
  public is_payable: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => CategoryAnalyze,{
    foreignKey: 'category_analyze_id'
  })
  public category_analyze: BelongsTo<typeof CategoryAnalyze>

  @manyToMany(() => InsuranceCompanyAnalyze,{
    pivotTable: 'insurance_company_analyzes',
    pivotForeignKey: 'analyze_id',
    pivotRelatedForeignKey: 'insurance_company_id',
    pivotTimestamps: true,
    pivotColumns: ['public_price','is_active']
  })
  public insurance_companies: ManyToMany<typeof InsuranceCompanyAnalyze>

  @hasMany(() => AnalyzeAskItem,{
    foreignKey: 'analyze_id',
    localKey: 'id'
  })
  public analyzes_ask_items: HasMany<typeof AnalyzeAskItem>
}
