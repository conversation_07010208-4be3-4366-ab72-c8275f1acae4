import { DateTime } from 'luxon'
import { BaseModel, afterFetch, column } from '@ioc:Adonis/Lucid/Orm'

type PaymentConfig = {
  amount: number;
  fees: number;
}

export default class PaymentType extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({columnName: 'name'})
  public name: string

  @column()
  public description: string | null;

  @column()
  public configs: PaymentConfig | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @afterFetch()
  public static setConfigs(type: PaymentType) {
    if (typeof type.configs === 'string') {
      type.configs = JSON.parse(type.configs);
    }
  }
}
