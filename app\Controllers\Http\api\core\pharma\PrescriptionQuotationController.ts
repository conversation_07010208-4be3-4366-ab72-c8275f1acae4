import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse, QuotationNotification, QuotationVersion } from 'App/Controllers/Utils/models'
import PharmacyController from './PharmacyController';
import QuotationPartner from 'App/Models/QuotationPartner';
import QuotationProposal from 'App/Models/QuotationProposal';
import QuotationRequest from 'App/Models/QuotationRequest';
import Order from 'App/Models/Order';
import Delivery from 'App/Models/Delivery';
import NatService from 'App/Services/NatService';
import Pharmacien from 'App/Models/Pharmacien';
import Patient from 'App/Models/Patient';
import Database from '@ioc:Adonis/Lucid/Database';
import QuotationProposalItem from 'App/Models/QuotationProposalItem';

export default class PrescriptionQuotationController extends PharmacyController {


  public async createQuotationProposal({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          quotation_request_id: schema.number(),
          items: schema.array().members(
            schema.object().members({
              prescription_item_id: schema.number(),
              quantity: schema.number(),
              unit_price: schema.number(),
              available_duration: schema.number.optional(),
              is_substitutable: schema.boolean.optional(),
            })
          ),
        })
      });
      const { quotation_request_id, items } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        status = 401;
        apiResponse = {
          message: "You are not authenticated",
          success: false,
          result: null,
        }
        return response.status(status).json(apiResponse);
      }
      const pharmacien = await Pharmacien.query().where('user_id', authUser.id).preload('pharmacy').first();
      if (!pharmacien) {
        status = 404;
        apiResponse = {
          message: "Le compte est introuvable",
          success: false,
          result: null,
        }
        return response.status(status).json(apiResponse);
      }
      const quotation = await QuotationRequest.query().where('id', quotation_request_id).forUpdate().first();
      if (!quotation) {
        status = 404;
        apiResponse = {
          message: "Demande de devis introuvable ou incorrect, veuille zréessayer plus tard",
          success: false,
          result: null,
          except: quotation,
        };
        return response.status(status).json(apiResponse);
      }

      let currentVersionId = Number(quotation.currentVersion);
      let versions: QuotationVersion[] = typeof quotation.versions === 'string' ? JSON.parse(quotation.versions as string) : quotation.versions;

      const partner = await QuotationPartner.query().where('quotation_request_id', quotation.id)
        .andWhere('pharmacy_id', pharmacien.pharmacyId)
        .andWhere('status', 'pending')
        .andWhere('version_id', currentVersionId)
        .forUpdate()
        .first();
      if (!partner) {
        status = 404;
        apiResponse = {
          message: "Aucun partenaire trouvé pour cette demande !",
          success: false,
          result: null,
          except: partner,
        };
        return response.status(status).json(apiResponse);
      }

      let notifications: QuotationNotification[] = typeof partner.notifications === 'string' ? JSON.parse(partner.notifications as string) : partner.notifications;

      const patient = await Patient.query().where('id', quotation.patientId).first();
      if (!patient) {
        status = 404;
        apiResponse = {
          message: "Patient introuvable, veuillez réessayez plus tard",
          success: false,
          result: null,
          except: patient,
        };
        return response.status(status).json(apiResponse);
      }
      const entity = pharmacien.pharmacy;
      if (entity) {
        const trx = await Database.transaction();
        try {
          let reference = Math.floor(********** + Math.random() * **********);

          let totalPrice = 0;
          items.forEach((item) => {
            totalPrice += item.unit_price * item.quantity;
          });
          const proposal = await QuotationProposal.create({
            reference: reference.toString(),
            pharmacyId: entity.id,
            quotationRequestId: quotation_request_id,
            proposalUserId: authUser.id,
            status: 'pending',
            totalPrice: totalPrice,
            totalItems: items.length,
            versionId: currentVersionId
          }, { client: trx });
          if (!proposal) {
            await trx.rollback();
            status = 500;
            apiResponse.message = "Une erreur serveur est survenue, veuillez réessayer plus tard";
            apiResponse.success = false;
            apiResponse.result = null;
            apiResponse.except = proposal;
            return response.status(status).json(apiResponse);
          }

          //get validated items from versions
          const validatedItems = versions
            .filter(version => version.status === 'pending' && version.version_id === currentVersionId)
            .map(version => version.items)
            .flat();

          // Vérifier que les éléments de la requête sont valides
          const validItems = items.filter(item => {
            return validatedItems.some(validItem => validItem.item_id === item.prescription_item_id && validItem.type === 'prescription');
          });

          if (validItems.length === 0) {
            await trx.rollback();
            status = 400;
            apiResponse = {
              message: "Aucun élément valide trouvé pour la version en cours",
              success: false,
              result: null,
            };
            return response.status(status).json(apiResponse);
          }

          //add items to quotation proposal
          const createdItems = validItems
            .filter(item => item.unit_price > 0)
            .map(item => ({
              quotationProposalId: proposal.id,
              prescriptionItemId: item.prescription_item_id,
              quantity: item.quantity,
              unitPrice: item.unit_price,
              availableDuration: item.available_duration !== undefined ? Number(item.available_duration) : undefined,
              status: 'pending' as 'pending',
            }));
          const proposalItems = await QuotationProposalItem.createMany(createdItems, { client: trx });
          if (!proposalItems) {
            await trx.rollback();
            status = 500;
            apiResponse = {
              message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
              success: false,
              result: null,
              except: proposalItems,
            };
            return response.status(status).json(apiResponse);
          }

          // update notifications
          const notification = notifications.find(notification => notification.version_id === currentVersionId && notification.status === 'pending');
          if (notification && notification !== undefined ) {
            notification.status = 'accepted';
            notification.notified_at = new Date().toISOString();

            const updatedNotifications = partner.notifications ? [...notifications, notification] : [notification];
            await partner
              .useTransaction(trx)
              .merge({
                status: 'accepted',
                notifications: JSON.stringify(updatedNotifications),
            }).save();
          }

          await quotation
            .useTransaction(trx)
            .merge({
              status: 'proposal_received',
            }).save();
          await trx.commit();
          //emit event

          let patientChannel = patient.channel;
          if (patientChannel) {
            const proposalData = await QuotationProposal.query()
              .where('pharmacy_id', entity.id)
              .andWhere('version_id', currentVersionId)
              .andWhere('quotation_request_id', quotation_request_id).orderBy('created_at', 'desc')
              .preload('proposalUser')
              .preload('pharmacy')
              .preload('laboratory')
              .preload('items', (query) => {
                query.preload('prescription_items', (query) => {
                  query.preload('product');
                });
              })
              .first();

            let data = {
              channel: patientChannel,
              proposal: proposalData,
            }
            //send to nats
            const nat = await NatService.getInstance();
            await nat.publish('quotation.proposal', data);
            console.log("publish to nats");

          }
          apiResponse = {
            success: true,
            message: "Proposition de devis envoyée avec succès",
            result: {
              proposal: proposal,
              items: proposalItems,
            },
          };
          return response.status(status).json(apiResponse);
        } catch (error) {
          console.log("error in create quotation proposal", error.message);
          await trx.rollback();
          status = 500;
          apiResponse = {
            message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
            success: false,
            result: null,
            except: error.message,
            errors: error.messages
          }
          return response.status(status).json(apiResponse);
        }
      }
    } catch (error) {
      status = 500;
      console.log("error in create quotation proposal", error.message);
      apiResponse = {
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        success: false,
        result: null,
        except: error.message,
        errors: error.messages
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async simulateQuotationProposal({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          entity: schema.string(),
          quotation_request_id: schema.number(),
          items: schema.array().members(
            schema.object().members({
              prescription_item_id: schema.number(),
              quantity: schema.number(),
              unit_price: schema.number(),
              available_duration: schema.number.optional(),
              is_substitutable: schema.boolean.optional(),
            })
          ),
        })
      });
      //@ts-ignore
      const { quotation_request_id, items, entity } = payload;

      let data = {
        proposal: {
          "status": "pending",
          "id": 9,
          "reference": "1607961097",
          "quotation_request_id": 14,
          "pharmacy_id": 8,
          "laboratory_id": null,
          "proposal_user_id": 67,
          "total_price": 25.5,
          "total_assured_price": null,
          "total_items": 2,
          "approved_at": null,
          "rejected_at": null,
          "expired_at": null,
          "created_at": "2024-11-18T17:13:51.000+00:00",
          "updated_at": "2024-11-18T17:13:51.000+00:00",
          "proposalUser": {
            "id": 67,
            "username": "ZENETY François Koffi",
            "email": "<EMAIL>",
            "phone": "99000010",
            "creator_id": null,
            "language_id": null,
            "country_id": 1,
            "role_id": 4,
            "status": 1,
            "firebase_instance_id": null,
            "online": 1,
            "email_verified_at": null,
            "phone_verified_at": null,
            "activated_at": null,
            "blocked_at": null,
            "profile_img": null,
            "token": null,
            "code_parrainage": "FG4HV9TL",
            "parrainage": {
              "plan": 1,
              "activeMoney": false,
              "active_qrcode": 0,
              "adhesion_fees": 0,
              "create_account": 0
            },
            "created_at": "2024-10-16T12:29:31.000+00:00",
            "updated_at": "2024-10-16T12:29:31.000+00:00"
          },
          "items": [
            {
              "id": 15,
              "quotation_proposal_id": 9,
              "prescription_item_id": 36,
              "analyze_ask_item_id": null,
              "quantity": 1,
              "unit_price": 10.5,
              "is_substitutable": 0,
              "is_assured": 0,
              "status": "pending",
              "quantity_paid": null,
              "paid_at": null,
              "available_duration": 7,
              "created_at": "2024-11-18T17:13:51.000+00:00",
              "updated_at": "2024-11-18T17:13:51.000+00:00"
            },
            {
              "id": 16,
              "quotation_proposal_id": 9,
              "prescription_item_id": 37,
              "analyze_ask_item_id": null,
              "quantity": 1,
              "unit_price": 15,
              "is_substitutable": 0,
              "is_assured": 0,
              "status": "pending",
              "quantity_paid": null,
              "paid_at": null,
              "available_duration": 7,
              "created_at": "2024-11-18T17:13:51.000+00:00",
              "updated_at": "2024-11-18T17:13:51.000+00:00"
            }
          ],
          "laboratory": null,
          "pharmacy": {
            "id": 8,
            "name": "PAHARMACIE HEDRANAWOE",
            "type": "externe",
            "phone": null,
            "email": null,
            "website": null,
            "country_id": 1,
            "city_id": 1,
            "quarter_id": null,
            "address": null,
            "location": null,
            "description": null,
            "opening_hours": null,
            "responsable": null,
            "personals": null,
            "status": "active",
            "created_at": "2024-09-11T17:24:30.000+00:00",
            "updated_at": "2024-09-11T17:24:30.000+00:00"
          }
        },
        channel: "YQemVXa4V9Q12SN3j1KSDk-mrS59W1FzcAH1xTnpsvoT0k_gIk5BNfIKHUeizESN"
      }
      //send to nats
      const nat = await NatService.getInstance();
      await nat.publish('quotation.proposal', data);

      apiResponse = {
        success: true,
        message: "Proposition de devis envoyée avec succès",
        result: {
          data: data,
        },
      };
      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      console.log("error in create quotation proposal", error.message);
      apiResponse = {
        message: "Une erreur serveur est survenue, veuillez réessayer plus tard",
        success: false,
        result: null,
        except: error.message,
        errors: error.messages
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async getQuotationRequests({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;

    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        apiResponse.message = "You are not authorized to access this resource";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pharmacyId = Number(personal.pharmacyId);

      // Fetch quotation requests that are addressed to the pharmacy
      let quotationRequests = await QuotationRequest.query()
        .innerJoin('quotation_partners', 'quotation_partners.quotation_request_id', 'quotation_requests.id')
        .where('quotation_partners.pharmacy_id', pharmacyId)
        .preload('patient')
        .preload('prescription', function (query) {
          query.preload('items', function (query) {
            query.preload('product').preload('substitutableProduct');
          });
        })
        .preload('proposals', function (query) {
          query.where('pharmacy_id', pharmacyId);
        })
        .paginate(page, limit);

      // Filter completed requests to include only those with proposals from the pharmacy
      const json_data = quotationRequests.toJSON();
      let data = json_data.data;

      apiResponse = {
        success: true,
        message: "Liste des demandes de devis adressées à la pharmacie",
        result: {
          meta: json_data.meta,
          data: data,
        }
      };
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
      status = 500;
    }

    return response.status(status).json(apiResponse);
  }


  public async getQuotationProposals({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {

      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        apiResponse.message = "You are not authorized to access this resource";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pharmacyId = Number(personal.pharmacyId);

      const quotationProposals = await QuotationProposal.query().where('pharmacy_id', pharmacyId).orderBy('created_at', 'desc')
        .preload('items')
        .preload('quotationRequest')
        .preload('proposalUser')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des propositions de devis",
        result: quotationProposals
      }

    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getQuotationRequestDetails({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const quotationRequestId = request.input('quotation_request_id');
      if (!quotationRequestId) {
        apiResponse.message = "Veuillez renseignez l'identifiant de la demande";
        status = 400;
      }

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        apiResponse.message = "You are not authorized to access this resource";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pharmacyId = Number(personal.pharmacyId);
      const checkExistQuotation = await QuotationPartner.query().where('quotation_request_id', quotationRequestId).where('pharmacy_id', pharmacyId).first();
      if (!checkExistQuotation) {
        apiResponse.message = "La demande de devis n'existe pas";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const quotation = await QuotationRequest.query().where('id', quotationRequestId)
        .preload('patient')
        .preload('prescription', function (q) {
          q.preload('items', function (query) {
            query.preload('product')
          })
        })
        .preload('analyzeAsk', function (query) {
          query.preload('items', function (query) {
            query.preload('analyze')
          })
        })
        .preload('proposals', function (query) {
          query.where('pharmacy_id', pharmacyId)
        })
        .preload('orders', function (query) {
          query.where('pharmacy_id', pharmacyId)
        })
        .first();

      if (!quotation) {
        apiResponse.message = "La demande de devis n'existe pas";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const prescription_status = await this.getPrescriptionStatus(quotation as QuotationRequest);

      apiResponse = {
        success: true,
        message: "Détails de la demande de devis",
        result: {
          quotation: quotation,
          prescription_status: prescription_status
        }
      }
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getQuotationPropositionDetails({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const propositionId = request.input('proposition_id');
      if (!propositionId) {
        apiResponse.message = "Veuillez renseignez l'identifiant de la proposition";
        status = 400;
      }

      const personal = await this.getAuthPersonal(auth);
      if (!personal) {
        apiResponse.message = "You are not authorized to access this resource";
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const pharmacyId = Number(personal.pharmacyId);

      const quotationProposal = await QuotationProposal.query().where('id', propositionId).where('pharmacy_id', pharmacyId)
        .preload('proposalUser')
        .preload('items')
        .preload('quotationRequest', function (query) {
          query.where('pharmacy_id', pharmacyId)
          query.preload('patient')
          query.preload('prescription')
        })
        .first();

      if (!quotationProposal) {
        apiResponse.message = "La proposition de devis n'existe pas";
        status = 404;
      }

      apiResponse = {
        success: true,
        message: "Détails de la proposition de devis",
        result: quotationProposal
      }
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getOrders({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;

      const pharmacyId = await this.getPharmacyIdByPersonal(auth);
      if (pharmacyId === null) {
        apiResponse = {
          success: false,
          message: "Vous n'avez pas accès à cette fonctionnalité",
          result: null
        }
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const orders = await Order.query().where('pharmacy_id', pharmacyId).orderBy('created_at', 'desc').withCount('items')
        .preload('quotation_request').preload('prescription').preload('patient')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des commandes",
        result: orders
      }
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      status = 500;
      apiResponse.result = null;
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getOrderDetails({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const orderId = request.input('order_id');
      const pharmacyId = await this.getPharmacyIdByPersonal(auth);
      if (pharmacyId === null) {
        apiResponse = {
          success: false,
          message: "Vous n'avez pas accès à cette fonctionnalité",
          result: null
        }
        status = 401;
        return response.status(status).json(apiResponse);
      }
      const order = await Order.query().where('id', orderId).where('pharmacy_id', pharmacyId)
        .preload('quotation_request')
        .preload('prescription')
        .preload('items', function (query) {
          query.preload('prescription_item').preload('quotation_proposal_item')
        })
        .preload('patient')
        .first();

      if (!order) {
        apiResponse.message = "La demande de devis n'existe pas";
        status = 404;
      }
      apiResponse = {
        success: true,
        message: "Détails de la demande de devis",
        result: order
      }
    } catch (error) {
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      status = 500;
      apiResponse.result = null;
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }

  public async getDeliveries({ response, request, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const delivery_status = request.input('status') as 'pending' | 'delivered' | 'cancelled' | 'failed' | 'validated' | 'prepared' | 'in_progress';

      const pharmacyId = await this.getPharmacyIdByPersonal(auth);
      if (pharmacyId === null) {
        apiResponse = {
          success: false,
          message: "Vous n'avez pas accès à cette fonctionnalité",
          result: null
        }
        status = 401;
        return response.status(status).json(apiResponse);
      }

      let query = Delivery.query().where('pharmacy_id', pharmacyId).where('delivery_type', 'prescription').orderBy('created_at', 'desc')
        .preload('prescription').preload('order').preload('patient').preload('quotationRequest');
      if (delivery_status) {
        query = query.where('status', delivery_status)
      }

      const deliveries = await query.paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des livraisons",
        result: deliveries
      }

    } catch (error) {
      status = 500;
      apiResponse.message = "Une erreur est survenue lors de la récupération des données";
      apiResponse.success = false;
      apiResponse.except = error.message;
    }
    return response.status(status).json(apiResponse);
  }


}
